#include "GetCsrFromDut.h"
#include <QHBoxLayout>
#include <QStandardPaths>

GetCsrFromDut::GetCsrFromDut()
{
    m_params = {
        {"SavePath", ""},
        {"UploadToYNSystem", true},
    };
}

QWidget* GetCsrFromDut::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    // Save Path Selection
    QLineEdit* savePathEdit = new QLineEdit(widget);
    savePathEdit->setText(m_params["SavePath"].toString());
    savePathEdit->setReadOnly(true);
    savePathEdit->setPlaceholderText(tr("Select save path..."));

    QPushButton* browseButton = new QPushButton(tr("Browse..."), widget);
    connect(browseButton, &QPushButton::clicked, [this, savePathEdit, widget]() {
        QString dir = QFileDialog::getExistingDirectory(
            widget,
            tr("Select CSR File Save Path"),
            m_params["SavePath"].toString().isEmpty() ?
                QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) :
                m_params["SavePath"].toString()
        );
        if (!dir.isEmpty()) {
            savePathEdit->setText(dir);
            m_params["SavePath"] = dir;
            emit parametersChanged();
        }
    });

    // Add path input and browse button to horizontal layout
    QHBoxLayout* pathLayout = new QHBoxLayout();
    pathLayout->addWidget(savePathEdit);
    pathLayout->addWidget(browseButton);
    pathLayout->setContentsMargins(0, 0, 0, 0);

    QWidget* pathWidget = new QWidget();
    pathWidget->setLayout(pathLayout);

    layout->addRow(tr("Save Path:"), pathWidget);

    // Upload Option
    QCheckBox* uploadCheckBox = new QCheckBox(tr("Upload to YN System"), widget);
    uploadCheckBox->setChecked(m_params["UploadToYNSystem"].toBool());
    connect(uploadCheckBox, &QCheckBox::toggled, [this](bool checked) {
        m_params["UploadToYNSystem"] = checked;
        emit parametersChanged();
    });

    layout->addRow(tr("Upload Option:"), uploadCheckBox);

    return widget;
}
