#include "ModelUidReader.h"
#include <QDebug>
#include <QFileInfo>

ModelUidReader::ModelUidReader()
    : m_isLoaded(false)
{
}

ModelUidReader::~ModelUidReader()
{
    clear();
}

bool ModelUidReader::loadExcelFile(const QString& filePath, QString& errorInfo)
{
    clear();
    
    if (filePath.isEmpty()) {
        errorInfo = "文件路径为空";
        return false;
    }
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        errorInfo = QString("文件不存在: %1").arg(filePath);
        return false;
    }
    
    if (!fileInfo.suffix().toLower().contains("xls")) {
        errorInfo = "文件格式不正确，请选择Excel文件 (.xlsx 或 .xls)";
        return false;
    }
    
    // 检查文件大小限制 (50MB)
    const qint64 maxFileSize = 50 * 1024 * 1024;  // 50MB
    if (fileInfo.size() > maxFileSize) {
        errorInfo = QString("文件太大 (%1 MB)，请选择小于50MB的文件")
                    .arg(fileInfo.size() / (1024.0 * 1024.0), 0, 'f', 2);
        return false;
    }
    
    QXlsx::Document document(filePath);
    if (!document.load()) {
        errorInfo = QString("无法加载Excel文件: %1").arg(filePath);
        return false;
    }
    
    if (!parseExcelData(&document, errorInfo)) {
        return false;
    }
    
    m_currentFilePath = filePath;
    m_isLoaded = true;
    
    qDebug() << QString("成功加载Excel文件: %1, 共解析 %2 条数据")
                .arg(filePath).arg(m_modelUidMap.size());
    
    return true;
}

bool ModelUidReader::parseExcelData(QXlsx::Document* document, QString& errorInfo)
{
    if (!document) {
        errorInfo = "Excel文档对象为空";
        return false;
    }
    
    QXlsx::CellRange range = document->dimension();
    if (!range.isValid()) {
        errorInfo = "Excel文件格式无效";
        return false;
    }
    
    int rowCount = range.rowCount();
    int columnCount = range.columnCount();
    
    if (rowCount < 2) {
        errorInfo = "Excel文件至少需要包含表头和一行数据";
        return false;
    }
    
    if (columnCount < 2) {
        errorInfo = "Excel文件至少需要包含Model和UID两列";
        return false;
    }
    
    // 查找Model和UID列的索引
    int modelColumnIndex = findColumnIndex(document, "model");
    int uidColumnIndex = findColumnIndex(document, "uid");
    
    if (modelColumnIndex == -1) {
        errorInfo = "未找到Model列，请确保表头包含以下任一列名: " + m_modelColumnNames.join(", ");
        return false;
    }
    
    if (uidColumnIndex == -1) {
        errorInfo = "未找到UID列，请确保表头包含以下任一列名: " + m_uidColumnNames.join(", ");
        return false;
    }
    
    // 从第二行开始读取数据
    for (int row = 2; row <= rowCount; ++row) {
        try {
            auto modelCell = document->cellAt(row, modelColumnIndex);
            auto uidCell = document->cellAt(row, uidColumnIndex);
            
            if (!modelCell || !uidCell) {
                qDebug() << QString("第 %1 行数据无效，跳过").arg(row);
                continue;
            }
            
            QString model = modelCell->value().toString().trimmed();
            QString uid = uidCell->value().toString().trimmed();
            
            if (model.isEmpty() || uid.isEmpty()) {
                continue;
            }
            
            // 检查重复的model
            if (m_modelUidMap.contains(model)) {
                qDebug() << QString("警告: 发现重复的型号 '%1'，将使用最新的UID: %2")
                            .arg(model).arg(uid);
            }
            
            m_modelUidMap.insert(model, uid);
        } catch (const std::exception& e) {
            qDebug() << QString("读取第 %1 行时发生异常: %2").arg(row).arg(e.what());
            continue;
        } catch (...) {
            qDebug() << QString("读取第 %1 行时发生未知异常").arg(row);
            continue;
        }
    }
    
    if (m_modelUidMap.isEmpty()) {
        errorInfo = "未找到有效的Model-UID数据，请检查Excel文件内容";
        return false;
    }
    
    return true;
}

int ModelUidReader::findColumnIndex(QXlsx::Document* document, const QString& columnType)
{
    int columnCount = document->dimension().columnCount();
    QStringList targetNames;
    
    if (columnType.toLower() == "model") {
        targetNames = m_modelColumnNames;
    } else if (columnType.toLower() == "uid") {
        targetNames = m_uidColumnNames;
    } else {
        return -1;
    }
    
    // 搜索表头行(第一行)
    for (int col = 1; col <= columnCount; ++col) {
        auto cell = document->cellAt(1, col);
        if (!cell) continue;
        
        QString cellValue = cell->value().toString().trimmed();
        
        for (const QString& targetName : targetNames) {
            if (cellValue.compare(targetName, Qt::CaseInsensitive) == 0) {
                return col;
            }
        }
    }
    
    return -1;
}

QStringList ModelUidReader::getModelList() const
{
    QStringList models = m_modelUidMap.keys();
    models.sort();
    return models;
}

QString ModelUidReader::getUidByModel(const QString& model) const
{
    return m_modelUidMap.value(model, QString());
}

void ModelUidReader::clear()
{
    m_modelUidMap.clear();
    m_isLoaded = false;
    m_currentFilePath.clear();
}

bool ModelUidReader::isLoaded() const
{
    return m_isLoaded && !m_modelUidMap.isEmpty();
}

int ModelUidReader::getDataCount() const
{
    return m_modelUidMap.size();
}