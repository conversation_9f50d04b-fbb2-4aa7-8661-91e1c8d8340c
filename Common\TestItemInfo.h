#ifndef TESTITEMINFO_H
#define TESTITEMINFO_H

#include <QString>
#include <QMap>
#include <QVariant>

typedef enum
{
    WRITE_BARCODE = 0,
    WRITE_IMEI,
    WRITE_IMEI2,
    WRITE_MEID,
    WRITE_BT,
    WRITE_WIFI,
    WRITE_ETHERNET_MAC,
    WRITE_SERIALNO,
    WRITE_NETCODE,
    E_UNKNOWN_CODE
}WriteData_Type_e;

static QString  ECodeStr[E_UNKNOWN_CODE] = {
    "SN",
    "IMEI1",
    "IMEI2",
    "MEID",
    "BtMac",
    "WifiMac",
    "EthernetMac",
    "SerialNo",
    "NetCode",
};

typedef enum
{
    WRITE_OBSN = 0,
    WRITE_IWSN,
    WRITE_BATLABEL,
    WRITE_SCRILSF,
    E_UNKNOWN_YDCODE
}YDData_Type_e;

static QString  EYDCodeStr[E_UNKNOWN_YDCODE] = {
    "<PERSON>BS<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>",
    "Scril.Sf",
};

#define HEADER_MAX_LENGTH 8
#define MAX_IMEI_NUMS 4
#define IMEI_MAX_LENGTH  15
#define IMEI_ARRAY_LEN (IMEI_MAX_LENGTH + 1)
#define BARCODE_MAX_LENGTH  64
#define BARCODE_ARRAY_LEN  BARCODE_MAX_LENGTH+1
#define BD_ADDR_LEN  6
#define BT_ADDRESS_MAX_LENGTH  ( BD_ADDR_LEN * 2 )
#define BT_ARRAY_LEN  ( BT_ADDRESS_MAX_LENGTH + 1 )
#define SERIAL_NO_LEN 19
#define SERIAL_NO_BUF_LEN (SERIAL_NO_LEN + 1)
#define NETCODE_LEN 31
#define NETCODE_BUF_LEN (NETCODE_LEN + 1)
#define MAC_ADDR_LEN  6
#define WIFI_MAC_MAX_LENGTH  ( MAC_ADDR_LEN * 2 )
#define WIFI_ARRAY_LEN  ( WIFI_MAC_MAX_LENGTH + 1)
#define EEPROM_SIZE 512
#define ETHERNET_MAC_MAX_LENGTH  (WIFI_MAC_MAX_LENGTH)
#define ETHERNET_MAC_ARRAY_LEN (WIFI_ARRAY_LEN)
#define DRMKEY_MCID_LENGTH 32
#define DRMKEY_MCID_ARRAY_LEN (DRMKEY_MCID_LENGTH + 1)
#define MEID_LENGTH 14
#define MEID_ARRAY_LEN (MEID_LENGTH + 1)
#define ESN_LENGTH 8
#define ESN_ARRAY_LEN (ESN_LENGTH + 1)

//DJ
#define DJSN_LEN 32
#define DJSN_BUF_LEN (DJSN_LEN + 1)

//TLX
#define TLXPUBLICKEY_LEN 1023
#define TLXPUBLICKEY_BUF_LEN (TLXPUBLICKEY_LEN + 1)

//LD
#define LDSN_LEN 32
#define LDSN_BUF_LEN (DJSN_LEN + 1)

//YD
#define SCRILSF_LENGTH 13
#define SCRILSF_ARRAY_LEN (SCRILSF_LENGTH + 1)

#define PROP_VALUE_MAX 92

typedef struct
{
    char strBarcode[BARCODE_ARRAY_LEN];
    char strBTAddr[BT_ARRAY_LEN];
    char strWifiAddr[WIFI_ARRAY_LEN];
    char strIMEI[MAX_IMEI_NUMS][IMEI_ARRAY_LEN];
    char strSerialNo[SERIAL_NO_BUF_LEN];
    char strEthernetMac[ETHERNET_MAC_ARRAY_LEN];
    char strEthernet2Mac[ETHERNET_MAC_ARRAY_LEN];
    char strDrmkeyMCID[DRMKEY_MCID_ARRAY_LEN];
    char strMeid[MEID_ARRAY_LEN];
    char strEsn[ESN_ARRAY_LEN];
    char strNetCode[NETCODE_LEN];
    //YD
    char strOBSN[BARCODE_ARRAY_LEN];
    char strIWSN[BARCODE_ARRAY_LEN];
    char strBatteryLabel[BARCODE_ARRAY_LEN];
    char strScrilSf[SCRILSF_ARRAY_LEN];
    //DJ
    char strDJSN1[DJSN_BUF_LEN];
    char strDJSN2[DJSN_BUF_LEN];
    //TLX
    char strTLXPublicKey[TLXPUBLICKEY_BUF_LEN];
    //LD
    char strLDSN[LDSN_BUF_LEN];
    //ACER
    char strACERSN[23];
}ScanData_struct;

typedef struct
{
    int iImeiNums;
    bool bDualIMEI;
    bool bThreeIMEI;
    bool bFourIMEI;
    bool bLockIMEI;
    bool bCheckSum;
    bool bDualIMEISame;
    bool bLockOtp;
}IMEIOption_struct;

#endif // TESTITEMINFO_H
