## Common 模块

- 职责：集中维护全局配置、测试流程常量、跨模块数据模型、TEE 版本管理和通用状态。
- 关键类型：CommonSettings_struct、MesConfig_struct、ScanData_struct、TestResult_Status 等
- 关键接口：
  - 配置与路径：get/setCfg/Json/CodesFilePath、load/saveCfg/Json
  - DUT 与端口：get/setDutPort、get/setActiveEnable、GPIB/电源参数
  - 测试流程参数：defaultOrderList、get/setTestCount、信号标志位等
  - TEE 管理：get/setCurrentTeeVersion、validateAndSyncTeeVersion、getDllTeeVersion
- 入口/引用面：被 MainWindow、TestItem 框架广泛使用

### 面包屑
- 返回：[仓库根 CLAUDE.md](../CLAUDE.md)

### 关键文件
- Common/Common.h、Common/Common.cpp
- Common/TestItemInfo.h（测试项公共定义）
- Common/3rdPartyDeviceCmd.h（设备命令常量）

