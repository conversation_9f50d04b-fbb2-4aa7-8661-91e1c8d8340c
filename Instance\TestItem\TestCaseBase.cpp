#include "TestCaseBase.h"
#include <QComboBox>
#include <QEvent>

class ComboBoxEventFilter : public QObject
{
public:
    explicit ComboBoxEventFilter(QObject* parent = nullptr) : QObject(parent) {}

protected:
    bool eventFilter(QObject* watched, QEvent* event) override
    {
        if (event->type() == QEvent::Wheel) {
            if (qobject_cast<QComboBox*>(watched)) {
                return true; // Eat the event
            }
        }
        return QObject::eventFilter(watched, event);
    }
};

TestCaseBase::TestCaseBase(QObject* parent) : QObject(parent), m_enabled(true)
{
}

void TestCaseBase::installComboBoxEventFilter(QWidget* widget)
{
    if (!widget)
        return;

    QList<QComboBox*> comboBoxes = widget->findChildren<QComboBox*>();
    for (QComboBox* cb : comboBoxes) {
        cb->installEventFilter(new ComboBoxEventFilter(cb));
    }
}

bool TestCaseBase::isEnabled() const
{
    return m_enabled;
}


void TestCaseBase::setEnabled(bool enabled)
{
    m_enabled = enabled;
    emit parametersChanged();
}
