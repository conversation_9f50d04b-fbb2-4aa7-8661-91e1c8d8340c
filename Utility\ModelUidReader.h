#ifndef MODELUIDREADER_H
#define MODELUIDREADER_H

#include <QString>
#include <QMap>
#include <QStringList>
#include <QSharedPointer>
#include "xlsxdocument.h"

struct ModelUidData
{
    QString model;
    QString uid;
    
    ModelUidData() = default;
    ModelUidData(const QString& m, const QString& u) : model(m), uid(u) {}
};

class ModelUidReader
{
public:
    ModelUidReader();
    ~ModelUidReader();
    
    // 加载Excel文件并解析Model和UID数据
    bool loadExcelFile(const QString& filePath, QString& errorInfo);
    
    // 获取所有型号列表
    QStringList getModelList() const;
    
    // 根据型号获取UID
    QString getUidByModel(const QString& model) const;
    
    // 清理数据
    void clear();
    
    // 检查是否已经加载数据
    bool isLoaded() const;
    
    // 获取数据总数
    int getDataCount() const;

private:
    bool parseExcelData(QXlsx::Document* document, QString& errorInfo);
    int findColumnIndex(QXlsx::Document* document, const QString& columnName);
    
private:
    QMap<QString, QString> m_modelUidMap;  // model -> uid 的映射
    bool m_isLoaded;
    QString m_currentFilePath;
    
    // 支持的列名变体
    QStringList m_modelColumnNames = {"Model", "型号", "model", "MODEL"};
    QStringList m_uidColumnNames = {"UID", "uid", "Uid", "UUID", "uuid"};
};

#endif // MODELUIDREADER_H