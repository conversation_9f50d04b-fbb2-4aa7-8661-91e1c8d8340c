#ifndef SETTINGCLASS_H
#define SETTINGCLASS_H

#include <QDialog>
#include <QStandardItemModel>
#include <QDoubleValidator>
#include <QMessageBox>
#include <QDir>
#include <QFileDialog>
#include <QIntValidator>
#include <QDoubleValidator>
#include <QEvent>

namespace Ui {
class MainWindow;
}

class MainWindow;

class SettingClass : public QDialog
{
    Q_OBJECT
public:
    SettingClass(MainWindow *mainWindow, Ui::MainWindow *MainUI = nullptr);

    void InitSettings();
    void SaveSettings();
    void InitPortListView();

protected:
    void changeEvent(QEvent* event) override;

private:
    void InitDlgUIProp();
    void InitLoginTypeUI();

private:
    Ui::MainWindow *m_ui;
    MainWindow *m_mainwindow;
};

#endif // SETTINGCLASS_H
