#ifndef TESTCASEBASE_H
#define TESTCASEBASE_H

#include <QObject>
#include <QWidget>
#include <QVariantMap>

class TestCaseBase : public QObject {
    Q_OBJECT
public:
    explicit TestCaseBase(QObject* parent = nullptr);

    virtual QString type() const = 0;
    virtual QString displayName() const = 0;
    virtual QWidget* createParameterWidget(QWidget* parent) = 0;
    virtual QVariantMap parameters() const = 0;
    virtual void setParameters(const QVariantMap& params) = 0;

    void installComboBoxEventFilter(QWidget* widget);

    bool isEnabled() const;
    void setEnabled(bool enabled);

signals:
    void parametersChanged();

protected:
    bool m_enabled = true;
    QVariantMap m_params;
};

#endif // TESTCASEBASE_H
