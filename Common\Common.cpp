#include "Common.h"
#include <QSettings>
#include "Instance/TestItem/TestCase/TeeSupportItem.h"
#include "3rdParty/KPHA-SDK-v5731/KPHAProxy.h"
using namespace std;

QSharedPointer<Common> Common::m_instance = QSharedPointer<Common>();
int g_TestStopFlag[MAX_SUPPORT_COMPORT_NUM];
hMTRACE  m_hDebugTrace[MAX_SUPPORT_COMPORT_NUM];

Common::Common()
{
    memset(&CommonSetting.g_sMetaComm, 0, sizeof(META_Common_struct));
    memset(&CommonSetting.m_TestCount, 0, sizeof(TestCount_struct)*MAX_SUPPORT_COMPORT_NUM);

    loadOptionFile();
    /*loadCfgFile(CommonSetting.CfgFilePath);
    loadJsonFile(CommonSetting.JsonFilePath);*/
    SettingInitPara();
    for(int i=0; i<MAX_SUPPORT_COMPORT_NUM; i++)
    {
        g_TestStopFlag[i] = 0;
        bDebugOn[i] = false;
    }
}

Common::~Common()
{

}

QSharedPointer<Common> Common::getInstance()
{
    // ONLY used in main thread, so NO NEED to care multi-thread condition.
    if (!m_instance) {
        m_instance = QSharedPointer<Common>(new Common());
    }
    return m_instance;
}

void Common::SettingInitPara()
{
    if(!FileUtils::IsFileExist(ABS_PATH_C("default.cfg")))
    {
        memcpy(CommonSetting.m_LogDir,  "C:\\Agenew_MultiCodes_Tool_log", sizeof(CommonSetting.m_LogDir));
        CommonSetting.g_sMetaComm.iPre_Connect_Timeout = CommonSetting.g_sMetaComm.iKernel_Connect_Timeout = 60000;
        CommonSetting.DutActive[0] = true;
    }

    if(!FileUtils::IsFileExist(CommonSetting.m_LogDir))
    {
        memcpy(CommonSetting.m_LogDir,  "C:\\Agenew_MultiCodes_Tool_log", sizeof(CommonSetting.m_LogDir));
    }

    if(m_testItemOrder.isEmpty())
    {
        setTestItemOrder(defaultOrderList);
    }
}

void Common::loadOptionFile()
{
    IniItem item("history.ini", "Common", "CfgFile");
    CommonSetting.CfgFilePath = item.GetStringValue();
    if(!FileUtils::IsFileExist(CommonSetting.CfgFilePath.toLocal8Bit().toStdString()))
    {
        CommonSetting.CfgFilePath = ABS_PATH_C("default.cfg");
    }

    item.SetItemName("JsonFile");
    CommonSetting.JsonFilePath = item.GetStringValue();
    if(!FileUtils::IsFileExist(CommonSetting.JsonFilePath.toLocal8Bit().toStdString()))
    {
        CommonSetting.JsonFilePath = ABS_PATH_C("testcases.json");
    }

    //Test Records
    IniItem item2("TestCount.ini", "TestCount", "Dut1_Count");
    CommonSetting.m_TestCount[0].CountNums = item2.GetIntValue();
    item2.SetItemName("Dut1_Pass");
    CommonSetting.m_TestCount[0].PassNums = item2.GetIntValue();
    item2.SetItemName("Dut1_Fail");
    CommonSetting.m_TestCount[0].FailNums = item2.GetIntValue();
    item2.SetItemName("Dut2_Count");
    CommonSetting.m_TestCount[1].CountNums = item2.GetIntValue();
    item2.SetItemName("Dut2_Pass");
    CommonSetting.m_TestCount[1].PassNums = item2.GetIntValue();
    item2.SetItemName("Dut2_Fail");
    CommonSetting.m_TestCount[1].FailNums = item2.GetIntValue();
    item2.SetItemName("Dut3_Count");
    CommonSetting.m_TestCount[2].CountNums = item2.GetIntValue();
    item2.SetItemName("Dut3_Pass");
    CommonSetting.m_TestCount[2].PassNums = item2.GetIntValue();
    item2.SetItemName("Dut3_Fail");
    CommonSetting.m_TestCount[2].FailNums = item2.GetIntValue();
    item2.SetItemName("Dut4_Count");
    CommonSetting.m_TestCount[3].CountNums = item2.GetIntValue();
    item2.SetItemName("Dut4_Pass");
    CommonSetting.m_TestCount[3].PassNums = item2.GetIntValue();
    item2.SetItemName("Dut4_Fail");
    CommonSetting.m_TestCount[3].FailNums = item2.GetIntValue();

    //Test Records
    IniItem MesItem("WorkStat.ini", "MES_Select", "MES");
    CommonSetting.m_MesConfig.MesType = MesItem.GetStringValue();
    MesItem.SetItemName("Enable_Mes_Offline");
    CommonSetting.m_MesConfig.MesEnable = MesItem.GetBooleanValue();
    MesItem.SetItemName("NotCheckStation");
    CommonSetting.m_MesConfig.NotCheckStationEnbale = MesItem.GetBooleanValue();
    MesItem.SetItemName("NotUpdateStation");
    CommonSetting.m_MesConfig.NotUpdateEnbale = MesItem.GetBooleanValue();
    MesItem.SetSectionName("SD_MES");
    MesItem.SetItemName("Po");
    CommonSetting.m_MesConfig.SDConfig.Order = MesItem.GetStringValue();
    MesItem.SetItemName("Station");
    CommonSetting.m_MesConfig.SDConfig.Station = MesItem.GetStringValue();
    MesItem.SetItemName("UserCode");
    CommonSetting.m_MesConfig.SDConfig.UserCode = MesItem.GetStringValue();
    MesItem.SetSectionName("manufacture");
    MesItem.SetItemName("Line");
    CommonSetting.m_MesConfig.AGNConfig.Line = MesItem.GetStringValue();
    MesItem.SetItemName("Order");
    CommonSetting.m_MesConfig.AGNConfig.Order = MesItem.GetStringValue();
    MesItem.SetItemName("Station");
    CommonSetting.m_MesConfig.AGNConfig.Station = MesItem.GetStringValue();
    MesItem.SetItemName("Worker");
    CommonSetting.m_MesConfig.AGNConfig.UserCode = MesItem.GetStringValue();
    MesItem.SetSectionName("Application");
    MesItem.SetItemName("Server");
    CommonSetting.m_MesConfig.AGNConfig.Server = MesItem.GetStringValue();
    MesItem.SetSectionName("YD_MES");
    MesItem.SetItemName("IP");
    CommonSetting.m_MesConfig.YDConfig.IP = MesItem.GetStringValue();
    MesItem.SetItemName("Mes_No");
    CommonSetting.m_MesConfig.YDConfig.Order = MesItem.GetStringValue();
    MesItem.SetItemName("Mes_Station");
    CommonSetting.m_MesConfig.YDConfig.Station = MesItem.GetStringValue();
    MesItem.SetItemName("Mes_ResNo");
    CommonSetting.m_MesConfig.YDConfig.ResName = MesItem.GetStringValue();

    // the order of test items
    /*QSettings TestItemSetting(ABS_PATH_C("TestItem.ini"), QSettings::IniFormat);
    TestItemSetting.beginGroup("TestItemOrder");
    m_testItemOrder = TestItemSetting.value("Order", QStringList()).toStringList();
    TestItemSetting.endGroup();*/
}

void Common::saveOptionFile()
{
    qDebug() << "Common::saveOptionFile() start !";
    QSettings settings(ABS_PATH_C("history.ini"),  QSettings::IniFormat);
    settings.beginGroup(QStringLiteral("Common"));
    settings.setValue(QStringLiteral("CfgFile"), CommonSetting.CfgFilePath);
    settings.setValue(QStringLiteral("JsonFile"), CommonSetting.JsonFilePath);
    settings.endGroup();
    settings.sync();

    //Test Count
    QSettings settings2(ABS_PATH_C("TestCount.ini"), QSettings::IniFormat);
    settings2.beginGroup(QStringLiteral("TestCount"));
    settings2.setValue(QStringLiteral("Dut1_Count"), CommonSetting.m_TestCount[0].CountNums);
    settings2.setValue(QStringLiteral("Dut1_Pass"), CommonSetting.m_TestCount[0].PassNums);
    settings2.setValue(QStringLiteral("Dut1_Fail"), CommonSetting.m_TestCount[0].FailNums);
    settings2.setValue(QStringLiteral("Dut2_Count"), CommonSetting.m_TestCount[1].CountNums);
    settings2.setValue(QStringLiteral("Dut2_Pass"), CommonSetting.m_TestCount[1].PassNums);
    settings2.setValue(QStringLiteral("Dut2_Fail"), CommonSetting.m_TestCount[1].FailNums);
    settings2.setValue(QStringLiteral("Dut3_Count"), CommonSetting.m_TestCount[2].CountNums);
    settings2.setValue(QStringLiteral("Dut3_Pass"), CommonSetting.m_TestCount[2].PassNums);
    settings2.setValue(QStringLiteral("Dut3_Fail"), CommonSetting.m_TestCount[2].FailNums);
    settings2.setValue(QStringLiteral("Dut4_Count"), CommonSetting.m_TestCount[3].CountNums);
    settings2.setValue(QStringLiteral("Dut4_Pass"), CommonSetting.m_TestCount[3].PassNums);
    settings2.setValue(QStringLiteral("Dut4_Fail"), CommonSetting.m_TestCount[3].FailNums);
    settings2.endGroup();
    settings2.sync();

    //Mes Setting
    QSettings MesSetting(ABS_PATH_C("WorkStat.ini"), QSettings::IniFormat);
    MesSetting.beginGroup(QStringLiteral("MES_Select"));
    MesSetting.setValue(QStringLiteral("MES"), CommonSetting.m_MesConfig.MesType);
    MesSetting.setValue(QStringLiteral("Enable_Mes_Offline"), CommonSetting.m_MesConfig.MesEnable);
    MesSetting.setValue(QStringLiteral("NotCheckStation"), CommonSetting.m_MesConfig.NotCheckStationEnbale);
    MesSetting.setValue(QStringLiteral("NotUpdateStation"), CommonSetting.m_MesConfig.NotUpdateEnbale);
    MesSetting.endGroup();

    MesSetting.beginGroup(QStringLiteral("SD_MES"));
    MesSetting.setValue(QStringLiteral("Po"), CommonSetting.m_MesConfig.SDConfig.Order);
    MesSetting.setValue(QStringLiteral("Station"), CommonSetting.m_MesConfig.SDConfig.Station);
    MesSetting.setValue(QStringLiteral("UserCode"), CommonSetting.m_MesConfig.SDConfig.UserCode);
    MesSetting.endGroup();

    MesSetting.beginGroup(QStringLiteral("manufacture"));
    MesSetting.setValue(QStringLiteral("Line"), CommonSetting.m_MesConfig.AGNConfig.Line);
    MesSetting.setValue(QStringLiteral("Order"), CommonSetting.m_MesConfig.AGNConfig.Order);
    MesSetting.setValue(QStringLiteral("Station"), CommonSetting.m_MesConfig.AGNConfig.Station);
    MesSetting.setValue(QStringLiteral("Worker"), CommonSetting.m_MesConfig.AGNConfig.UserCode);
    MesSetting.endGroup();

    MesSetting.beginGroup(QStringLiteral("Application"));
    MesSetting.setValue(QStringLiteral("Server"), CommonSetting.m_MesConfig.AGNConfig.Server);
    MesSetting.endGroup();

    MesSetting.beginGroup(QStringLiteral("YD_MES"));
    MesSetting.setValue(QStringLiteral("IP"), CommonSetting.m_MesConfig.YDConfig.IP);
    MesSetting.setValue(QStringLiteral("Mes_No"), CommonSetting.m_MesConfig.YDConfig.Order);
    MesSetting.setValue(QStringLiteral("Mes_Station"), CommonSetting.m_MesConfig.YDConfig.Station);
    MesSetting.setValue(QStringLiteral("Mes_ResNo"), CommonSetting.m_MesConfig.YDConfig.ResName);
    MesSetting.endGroup();

    MesSetting.sync();

    // the order of test items
    /*QSettings TestItemSetting(ABS_PATH_C("TestItem.ini"), QSettings::IniFormat);
    TestItemSetting.beginGroup("TestItemOrder");
    TestItemSetting.setValue("Order", m_testItemOrder);
    TestItemSetting.endGroup();*/

    qDebug() << "Common::saveOptionFile() end !";
}

void Common::loadCfgFile(QString cfgpath)
{
    QSettings settings(CommonSetting.CfgFilePath,  QSettings::IniFormat);

    settings.beginGroup(QStringLiteral("Common"));
    CommonSetting.m_CfgFilePara.WifiOnlyEnable = settings.value("WifiOnly").toBool();
    CommonSetting.g_sMetaComm.sDBFileOption.bAPDBFromDUT = settings.value("LoadApFromDut").toBool();
    CommonSetting.g_sMetaComm.sDBFileOption.bMDDBFromDUT = settings.value("LoadMdFromDut").toBool();
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strAPDbpath, "%s", settings.value("ApDBPath").toString().toStdString().c_str());
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strMD1Dbpath, "%s", settings.value("MdDBPath").toString().toStdString().c_str());
    CommonSetting.AdbServiceEnable = settings.value("AdbService").toBool();
    sprintf_s(CommonSetting.m_LogDir, "%s", settings.value("LogPath").toString().toStdString().c_str());
    CommonSetting.g_sMetaComm.iPre_Connect_Timeout = settings.value("PLPortTimeout").toInt();
    CommonSetting.g_sMetaComm.iKernel_Connect_Timeout = settings.value("KLPortTimeout").toInt();
    settings.endGroup();

    settings.beginGroup(QStringLiteral("TestCfg"));
    CommonSetting.m_CfgFilePara.NetCodeType = settings.value("NetCodeType").toInt();
    CommonSetting.UseRelayEnbale = settings.value("UseRelay").toBool();
    CommonSetting.UseScanGunEnable = settings.value("UseScanGun").toBool();
    CommonSetting.iPowerType = (PowerType)settings.value("PowerType").toInt();
    CommonSetting.iUsbControlType = (UsbControlType)settings.value("UsbControl").toInt();
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut1_Config"));
    CommonSetting.DutActive[0] = settings.value("Dut_Enable").toBool();
    CommonSetting.m_sComArg[0].nPreLoaderComNum = settings.value("Preloader_Port").toInt();
    CommonSetting.m_sComArg[0].nKernelComNum = settings.value("Kernel_Port").toInt();
    CommonSetting.m_sComArg[0].nControlBoxComNum = settings.value("ControlBox_Port").toInt();
    CommonSetting.m_sComArg[0].nScanGunComNum = settings.value("ScanGun_Port").toInt();
    CommonSetting.m_sPSArg[0].GPIBAddress = settings.value("DUT_GPIB_Address").toString();
    CommonSetting.m_sPSArg[0].Output1Voltage = settings.value("Output1_Voltage").toFloat();
    CommonSetting.m_sPSArg[0].Output1Current = settings.value("Output1_Current").toFloat();
    CommonSetting.m_sPSArg[0].Output2Voltage = settings.value("Output2_Voltage").toFloat();
    CommonSetting.m_sPSArg[0].Output2Current = settings.value("Output2_Current").toFloat();
    CommonSetting.m_sPSArg[0].nBluComNum = settings.value("BluComNum").toInt();
    CommonSetting.m_sPSArg[0].BluOutputVoltage = settings.value("BluVoltage").toFloat();
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut2_Config"));
    CommonSetting.DutActive[1] = settings.value("Dut_Enable").toBool();
    CommonSetting.m_sComArg[1].nPreLoaderComNum = settings.value("Preloader_Port").toInt();
    CommonSetting.m_sComArg[1].nKernelComNum = settings.value("Kernel_Port").toInt();
    CommonSetting.m_sComArg[1].nControlBoxComNum = settings.value("ControlBox_Port").toInt();
    CommonSetting.m_sComArg[1].nScanGunComNum = settings.value("ScanGun_Port").toInt();
    CommonSetting.m_sPSArg[1].GPIBAddress = settings.value("DUT_GPIB_Address").toString();
    CommonSetting.m_sPSArg[1].Output1Voltage = settings.value("Output1_Voltage").toFloat();
    CommonSetting.m_sPSArg[1].Output1Current = settings.value("Output1_Current").toFloat();
    CommonSetting.m_sPSArg[1].Output2Voltage = settings.value("Output2_Voltage").toFloat();
    CommonSetting.m_sPSArg[1].Output2Current = settings.value("Output2_Current").toFloat();
    CommonSetting.m_sPSArg[1].nBluComNum = settings.value("BluComNum").toInt();
    CommonSetting.m_sPSArg[1].BluOutputVoltage = settings.value("BluVoltage").toFloat();
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut3_Config"));
    CommonSetting.DutActive[2] = settings.value("Dut_Enable").toBool();
    CommonSetting.m_sComArg[2].nPreLoaderComNum = settings.value("Preloader_Port").toInt();
    CommonSetting.m_sComArg[2].nKernelComNum = settings.value("Kernel_Port").toInt();
    CommonSetting.m_sComArg[2].nControlBoxComNum = settings.value("ControlBox_Port").toInt();
    CommonSetting.m_sComArg[2].nScanGunComNum = settings.value("ScanGun_Port").toInt();
    CommonSetting.m_sPSArg[2].GPIBAddress = settings.value("DUT_GPIB_Address").toString();
    CommonSetting.m_sPSArg[2].Output1Voltage = settings.value("Output1_Voltage").toFloat();
    CommonSetting.m_sPSArg[2].Output1Current = settings.value("Output1_Current").toFloat();
    CommonSetting.m_sPSArg[2].Output2Voltage = settings.value("Output2_Voltage").toFloat();
    CommonSetting.m_sPSArg[2].Output2Current = settings.value("Output2_Current").toFloat();
    CommonSetting.m_sPSArg[2].nBluComNum = settings.value("BluComNum").toInt();
    CommonSetting.m_sPSArg[2].BluOutputVoltage = settings.value("BluVoltage").toFloat();
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut4_Config"));
    CommonSetting.DutActive[3] = settings.value("Dut_Enable").toBool();
    CommonSetting.m_sComArg[3].nPreLoaderComNum = settings.value("Preloader_Port").toInt();
    CommonSetting.m_sComArg[3].nKernelComNum = settings.value("Kernel_Port").toInt();
    CommonSetting.m_sComArg[3].nControlBoxComNum = settings.value("ControlBox_Port").toInt();
    CommonSetting.m_sComArg[3].nScanGunComNum = settings.value("ScanGun_Port").toInt();
    CommonSetting.m_sPSArg[3].GPIBAddress = settings.value("DUT_GPIB_Address").toString();
    CommonSetting.m_sPSArg[3].Output1Voltage = settings.value("Output1_Voltage").toFloat();
    CommonSetting.m_sPSArg[3].Output1Current = settings.value("Output1_Current").toFloat();
    CommonSetting.m_sPSArg[3].Output2Voltage = settings.value("Output2_Voltage").toFloat();
    CommonSetting.m_sPSArg[3].Output2Current = settings.value("Output2_Current").toFloat();
    CommonSetting.m_sPSArg[3].nBluComNum = settings.value("BluComNum").toInt();
    CommonSetting.m_sPSArg[3].BluOutputVoltage = settings.value("BluVoltage").toFloat();
    settings.endGroup();

    settings.sync();
}

void Common::saveCfgFile(QString cfgpath)
{
    qDebug() << "Common::saveCfgFile() start !";
    QSettings settings(CommonSetting.CfgFilePath,  QSettings::IniFormat);

    settings.beginGroup(QStringLiteral("Common"));
    settings.setValue(QStringLiteral("WifiOnly"), CommonSetting.m_CfgFilePara.WifiOnlyEnable);
    settings.setValue(QStringLiteral("LoadApFromDut"), CommonSetting.g_sMetaComm.sDBFileOption.bAPDBFromDUT);
    settings.setValue(QStringLiteral("LoadMdFromDut"), CommonSetting.g_sMetaComm.sDBFileOption.bMDDBFromDUT);
    settings.setValue(QStringLiteral("ApDBPath"), QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strAPDbpath));
    settings.setValue(QStringLiteral("MdDBPath"), QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strMD1Dbpath));
    settings.setValue(QStringLiteral("AdbService"), CommonSetting.AdbServiceEnable);
    settings.setValue(QStringLiteral("LogPath"), QString("%1").arg(CommonSetting.m_LogDir));
    settings.endGroup();

    settings.beginGroup(QStringLiteral("TestCfg"));
    settings.setValue(QStringLiteral("NetCodeType"), CommonSetting.m_CfgFilePara.NetCodeType);
    settings.setValue(QStringLiteral("UseRelay"), CommonSetting.UseRelayEnbale);
    settings.setValue(QStringLiteral("UseScanGun"), CommonSetting.UseScanGunEnable);
    settings.setValue(QStringLiteral("PowerType"), (int)CommonSetting.iPowerType);
    settings.setValue(QStringLiteral("UsbControl"), (int)CommonSetting.iUsbControlType);
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut1_Config"));
    settings.setValue(QStringLiteral("Dut_Enable"), CommonSetting.DutActive[0]);
    settings.setValue(QStringLiteral("Preloader_Port"), CommonSetting.m_sComArg[0].nPreLoaderComNum);
    settings.setValue(QStringLiteral("Kernel_Port"), CommonSetting.m_sComArg[0].nKernelComNum);
    settings.setValue(QStringLiteral("ControlBox_Port"), CommonSetting.m_sComArg[0].nControlBoxComNum);
    settings.setValue(QStringLiteral("ScanGun_Port"), CommonSetting.m_sComArg[0].nScanGunComNum);
	settings.setValue(QStringLiteral("DUT_GPIB_Address"), CommonSetting.m_sPSArg[0].GPIBAddress);
    settings.setValue(QStringLiteral("Output1_Voltage"), QString::number(CommonSetting.m_sPSArg[0].Output1Voltage));
    settings.setValue(QStringLiteral("Output1_Current"), QString::number(CommonSetting.m_sPSArg[0].Output1Current));
    settings.setValue(QStringLiteral("Output2_Voltage"), QString::number(CommonSetting.m_sPSArg[0].Output2Voltage));
    settings.setValue(QStringLiteral("Output2_Current"), QString::number(CommonSetting.m_sPSArg[0].Output2Current));
    settings.setValue(QStringLiteral("BluComNum"), QString::number(CommonSetting.m_sPSArg[0].nBluComNum));
    settings.setValue(QStringLiteral("BluVoltage"), QString::number(CommonSetting.m_sPSArg[0].BluOutputVoltage, 'f'));
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut2_Config"));
    settings.setValue(QStringLiteral("Dut_Enable"), CommonSetting.DutActive[1]);
    settings.setValue(QStringLiteral("Preloader_Port"), CommonSetting.m_sComArg[1].nPreLoaderComNum);
    settings.setValue(QStringLiteral("Kernel_Port"), CommonSetting.m_sComArg[1].nKernelComNum);
    settings.setValue(QStringLiteral("ControlBox_Port"), CommonSetting.m_sComArg[1].nControlBoxComNum);
    settings.setValue(QStringLiteral("ScanGun_Port"), CommonSetting.m_sComArg[1].nScanGunComNum);
	settings.setValue(QStringLiteral("DUT_GPIB_Address"), CommonSetting.m_sPSArg[1].GPIBAddress);
    settings.setValue(QStringLiteral("Output1_Voltage"), QString::number(CommonSetting.m_sPSArg[1].Output1Voltage));
    settings.setValue(QStringLiteral("Output1_Current"), QString::number(CommonSetting.m_sPSArg[1].Output1Current));
    settings.setValue(QStringLiteral("Output2_Voltage"), QString::number(CommonSetting.m_sPSArg[1].Output2Voltage));
    settings.setValue(QStringLiteral("Output2_Current"), QString::number(CommonSetting.m_sPSArg[1].Output2Current));
    settings.setValue(QStringLiteral("BluComNum"), QString::number(CommonSetting.m_sPSArg[1].nBluComNum));
    settings.setValue(QStringLiteral("BluVoltage"), QString::number(CommonSetting.m_sPSArg[1].BluOutputVoltage, 'f'));
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut3_Config"));
    settings.setValue(QStringLiteral("Dut_Enable"), CommonSetting.DutActive[2]);
    settings.setValue(QStringLiteral("Preloader_Port"), CommonSetting.m_sComArg[2].nPreLoaderComNum);
    settings.setValue(QStringLiteral("Kernel_Port"), CommonSetting.m_sComArg[2].nKernelComNum);
    settings.setValue(QStringLiteral("ControlBox_Port"), CommonSetting.m_sComArg[2].nControlBoxComNum);
    settings.setValue(QStringLiteral("ScanGun_Port"), CommonSetting.m_sComArg[2].nScanGunComNum);
	settings.setValue(QStringLiteral("DUT_GPIB_Address"), CommonSetting.m_sPSArg[2].GPIBAddress);
    settings.setValue(QStringLiteral("Output1_Voltage"), QString::number(CommonSetting.m_sPSArg[2].Output1Voltage));
    settings.setValue(QStringLiteral("Output1_Current"), QString::number(CommonSetting.m_sPSArg[2].Output1Current));
    settings.setValue(QStringLiteral("Output2_Voltage"), QString::number(CommonSetting.m_sPSArg[2].Output2Voltage));
    settings.setValue(QStringLiteral("Output2_Current"), QString::number(CommonSetting.m_sPSArg[2].Output2Current));
    settings.setValue(QStringLiteral("BluComNum"), QString::number(CommonSetting.m_sPSArg[2].nBluComNum));
    settings.setValue(QStringLiteral("BluVoltage"), QString::number(CommonSetting.m_sPSArg[2].BluOutputVoltage, 'f'));
    settings.endGroup();

    settings.beginGroup(QStringLiteral("Dut4_Config"));
    settings.setValue(QStringLiteral("Dut_Enable"), CommonSetting.DutActive[3]);
    settings.setValue(QStringLiteral("Preloader_Port"), CommonSetting.m_sComArg[3].nPreLoaderComNum);
    settings.setValue(QStringLiteral("Kernel_Port"), CommonSetting.m_sComArg[3].nKernelComNum);
    settings.setValue(QStringLiteral("ControlBox_Port"), CommonSetting.m_sComArg[3].nControlBoxComNum);
    settings.setValue(QStringLiteral("ScanGun_Port"), CommonSetting.m_sComArg[3].nScanGunComNum);
	settings.setValue(QStringLiteral("DUT_GPIB_Address"), CommonSetting.m_sPSArg[3].GPIBAddress);
    settings.setValue(QStringLiteral("Output1_Voltage"), QString::number(CommonSetting.m_sPSArg[3].Output1Voltage));
    settings.setValue(QStringLiteral("Output1_Current"), QString::number(CommonSetting.m_sPSArg[3].Output1Current));
    settings.setValue(QStringLiteral("Output2_Voltage"), QString::number(CommonSetting.m_sPSArg[3].Output2Voltage));
    settings.setValue(QStringLiteral("Output2_Current"), QString::number(CommonSetting.m_sPSArg[3].Output2Current));
    settings.setValue(QStringLiteral("BluComNum"), QString::number(CommonSetting.m_sPSArg[3].nBluComNum));
    settings.setValue(QStringLiteral("BluVoltage"), QString::number(CommonSetting.m_sPSArg[3].BluOutputVoltage, 'f'));
    settings.endGroup();

    settings.sync();

    qDebug() << "Common::saveCfgFile() end !";
}

void Common::loadJsonFile(QString jsonpath)
{
    QString JsonFilePath = jsonpath;
    QFile file(JsonFilePath);

    if (file.exists() && file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        CommonSetting.m_ItemJsonDoc = QJsonDocument::fromJson(data);
    }
}

QJsonDocument Common::getItemJsonDoc()
{
    return CommonSetting.m_ItemJsonDoc;
}

QJsonObject Common::getJsonParams(QString testcase)
{
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
            return testCaseObj["params"].toObject();
    }

    return QJsonObject();
}

void Common::setJsonParams(QString testcase, QJsonObject jsonObj)
{
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    bool found = false;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString()) {
            testCaseObj["params"] = jsonObj;
            testCasesArray[i] = testCaseObj;
            found = true;
            break;
        }
    }

    if (!found) {
        QJsonObject newTestCaseObj;
        newTestCaseObj["type"] = testcase;
        newTestCaseObj["params"] = jsonObj;
        testCasesArray.append(newTestCaseObj);
    }

    CommonSetting.m_ItemJsonDoc.setArray(testCasesArray);
    QFile file(CommonSetting.JsonFilePath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(CommonSetting.m_ItemJsonDoc.toJson());
        file.close();
    }
    else
    {
        qDebug() << "Failed to open JSON file for writing.";
    }
}

bool Common::getItemEnable(QString testcase)
{
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
            return testCaseObj["enable"].toBool();
    }

    return false;
}

//char* - > unicode QString
QString Common::ConvertStdString(const char* str)
{
    static QTextCodec *code = QTextCodec::codecForLocale(); //解决中文路径问题
    return code->toUnicode( str );
}

//Qstring to const char *   //unicode -> utf8
const char * Common::ConvertQString( const QString& str)
{
    static QTextCodec *code = QTextCodec::codecForLocale(); //解决中文路径问题

    return code->fromUnicode(str).constData();
}

//utf8 -> GBK
QString Common::convertUtf8ToGbk(const QString &utf8String)
{
    QTextCodec *codec = QTextCodec::codecForName("GBK");
    QString gbkString = codec->toUnicode(utf8String.toUtf8(), utf8String.length());
    return gbkString;
}

//GBK -> utf8
QString Common::ConvertANSIToUTF8(const std::string& ansiString)
{
    QTextCodec* codec = QTextCodec::codecForName("Windows-1252"); // 对于ANSI编码，通常使用Windows-1252
    QString qstr = codec->toUnicode(ansiString.c_str());
    return qstr;
}

/*
 * Common
 */
QString Common::getCfgFilePath() const
{
    return CommonSetting.CfgFilePath;
}

void Common::setCfgFilePath(QString str)
{
    CommonSetting.CfgFilePath = str;
}

QString Common::getJsonFilePath() const
{
    return CommonSetting.JsonFilePath;
}

void Common::setJsonFilePath(QString str)
{
    CommonSetting.JsonFilePath = str;
}

QString Common::getCodesFilePath() const
{
    return CommonSetting.CodesFilePath;
}

void Common::setCodesFilePath(QString str)
{
    CommonSetting.CodesFilePath = str;
}

bool Common::getWifionlyEnable() const
{
    return CommonSetting.m_CfgFilePara.WifiOnlyEnable;
}

void Common::setWifionlyEnable(bool Enable)
{
    CommonSetting.m_CfgFilePara.WifiOnlyEnable = Enable;
}

bool Common::getADBServiceEnable() const
{
    return CommonSetting.AdbServiceEnable;
}

void Common::setADBServiceEnable(bool Enable)
{
    CommonSetting.AdbServiceEnable = Enable;
}

bool Common::getLoadApFromDutEnable() const
{
    return CommonSetting.g_sMetaComm.sDBFileOption.bAPDBFromDUT;
}

void Common::setLoadApFromDutEnable(bool Enable)
{
    CommonSetting.g_sMetaComm.sDBFileOption.bAPDBFromDUT = Enable;
}

bool Common::getLoadMdFromDutEnable() const
{
    return CommonSetting.g_sMetaComm.sDBFileOption.bMDDBFromDUT;
}

void Common::setLoadMdFromDutEnable(bool Enable)
{
    CommonSetting.g_sMetaComm.sDBFileOption.bMDDBFromDUT = Enable;
}

QString Common::getAPDbPath() const
{
    return QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strAPDbpath);
}

void Common::setAPDbPath(QString str)
{
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strAPDbpath, "%s", str.toStdString().c_str());
}

QString Common::getMdDbPath() const
{
    return QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strMD1Dbpath);
}

void Common::setMdDbPath(QString str)
{
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strMD1Dbpath, "%s", str.toStdString().c_str());
}

QString Common::getLogPath() const
{
    return QString("%1").arg(CommonSetting.m_LogDir);
}

void Common::setLogPath(QString str)
{
    sprintf_s(CommonSetting.m_LogDir, "%s", str.toStdString().c_str());
}

int Common::getNetCodeType() const
{
    return CommonSetting.m_CfgFilePara.NetCodeType;
}

void Common::setNetCodeType(int type)
{
    CommonSetting.m_CfgFilePara.NetCodeType = type;
}

bool Common::getUseRelayEnable() const
{
    return CommonSetting.UseRelayEnbale;
}

void Common::setUseRelayEnable(bool Enable)
{
    CommonSetting.UseRelayEnbale = Enable;
}

bool Common::getUseScanGunEnable() const
{
    return CommonSetting.UseScanGunEnable;
}

void Common::setUseScanGunEnable(bool Enable)
{
    CommonSetting.UseScanGunEnable = Enable;
}

/*
 * Dut Config
 */
bool Common::getActiveEnable(int nThreadID) const
{
    return CommonSetting.DutActive[nThreadID];
}

void Common::setActiveEnable(int nThreadID, bool Enable)
{
    CommonSetting.DutActive[nThreadID] = Enable;
}

int Common::getDutPort(int nThreadID, PORT_TYPE porttype) const
{
    int port = 0;
    if(porttype == PRELOADER_COM)
        port = CommonSetting.m_sComArg[nThreadID].nPreLoaderComNum;
    else if(porttype == KERNEL_COM)
        port = CommonSetting.m_sComArg[nThreadID].nKernelComNum;
    else if(porttype == CONTROLBOX_COM)
        port = CommonSetting.m_sComArg[nThreadID].nControlBoxComNum;
    else if(porttype == SCANGUN_COM)
        port = CommonSetting.m_sComArg[nThreadID].nScanGunComNum;

    if(port < 0)
        return 0;
    else
        return port;
}

void Common::setDutPort(int nThreadID, PORT_TYPE porttype, int port)
{
    if(porttype == PRELOADER_COM)
        CommonSetting.m_sComArg[nThreadID].nPreLoaderComNum = port;
    else if(porttype == KERNEL_COM)
        CommonSetting.m_sComArg[nThreadID].nKernelComNum = port;
    else if(porttype == CONTROLBOX_COM)
        CommonSetting.m_sComArg[nThreadID].nControlBoxComNum = port;
    else if(porttype == SCANGUN_COM)
        CommonSetting.m_sComArg[nThreadID].nScanGunComNum = port;
}

QString Common::getDutGPIBAddress(int nThreadID) const
{
    return CommonSetting.m_sPSArg[nThreadID].GPIBAddress;
}

void Common::setDutGPIBAddress(int nThreadID, QString str)
{
    CommonSetting.m_sPSArg[nThreadID].GPIBAddress = str;
}

float Common::getDutOutput1Vol(int nThreadID) const
{
    float value = CommonSetting.m_sPSArg[nThreadID].Output1Voltage;
    if(value < 0)
        return 0;
    else
        return value;
}

void Common::setDutOutput1Vol(int nThreadID, float value)
{
    CommonSetting.m_sPSArg[nThreadID].Output1Voltage = value;
}

float Common::getDutOutput1Current(int nThreadID) const
{
    float value = CommonSetting.m_sPSArg[nThreadID].Output1Current;
    if(value < 0)
        return 0;
    else
        return value;
}

void Common::setDutOutput1Current(int nThreadID, float value)
{
    CommonSetting.m_sPSArg[nThreadID].Output1Current = value;
}

float Common::getDutOutput2Vol(int nThreadID) const
{
    float value = CommonSetting.m_sPSArg[nThreadID].Output2Voltage;
    if(value < 0)
        return 0;
    else
        return value;
}

void Common::setDutOutput2Vol(int nThreadID, float value)
{
    CommonSetting.m_sPSArg[nThreadID].Output2Voltage = value;
}

float Common::getDutOutput2Current(int nThreadID) const
{
    float value = CommonSetting.m_sPSArg[nThreadID].Output2Current;
    if(value < 0)
        return 0;
    else
        return value;
}

void Common::setDutOutput2Current(int nThreadID, float value)
{
    CommonSetting.m_sPSArg[nThreadID].Output2Current = value;
}

UINT Common::getDutBluPort(int nThreadID) const
{
    return CommonSetting.m_sPSArg[nThreadID].nBluComNum;
}

void Common::setDutBluPort(int nThreadID, UINT port)
{
    CommonSetting.m_sPSArg[nThreadID].nBluComNum = port;
}

float Common::getDutBluOutputVol(int nThreadID) const
{
    return CommonSetting.m_sPSArg[nThreadID].BluOutputVoltage;
}

void Common::setDutBluOutputVol(int nThreadID, float value)
{
    CommonSetting.m_sPSArg[nThreadID].BluOutputVoltage = value;
}

//Meta Function
QString Common::getBromFilter() const
{
    return QString("VID_0E8D&PID_0003");
}

QString Common::getPreloaderFilter() const
{
    return QString("VID_0E8D&PID_2000 VID_0525&PID_A4A7 VID_1004&PID_6000");
}

QString Common::getKernelFilter() const
{
    return QString("VID_0E8D&PID_2007 VID_0E8D&PID_2006&MI_02 VID_0E8D&PID_2040&MI_02 VID_0BB4&PID_0005&MI_02 VID_1004&PID_6000 VID_0E8D&PID_202D&MI_01 VID_0E8D&PID_200E&MI_01 VID_0E8D&PID_2026&MI_02 VID_0E8D&PID_2040&MI_02 VID_0E8D&PID_7101&MI_00");
}

UINT Common::getPLTimeout() const
{
    return CommonSetting.g_sMetaComm.iPre_Connect_Timeout;
}

UINT Common::getKLTimeout() const
{
    return CommonSetting.g_sMetaComm.iKernel_Connect_Timeout;
}

bool Common::getAPDBInitFlag(int nThreadID) const
{
    return CommonSetting.g_sMetaComm.sDBFileOption.bDBInitAP[nThreadID];
}

void Common::setAPDBInitFlag(int nThreadID, bool Enable)
{
    CommonSetting.g_sMetaComm.sDBFileOption.bDBInitAP[nThreadID] = Enable;
}

bool Common::getMD1DBInitFlag(int nThreadID) const
{
    return CommonSetting.g_sMetaComm.sDBFileOption.bDBInitModem_1[nThreadID];
}

void Common::setMD1DBInitFlag(int nThreadID, bool Enable)
{
    CommonSetting.g_sMetaComm.sDBFileOption.bDBInitModem_1[nThreadID] = Enable;
}

bool Common::getMD2DBInitFlag(int nThreadID) const
{
    return CommonSetting.g_sMetaComm.sDBFileOption.bDBInitModem_2[nThreadID];
}

void Common::setMD2DBInitFlag(int nThreadID, bool Enable)
{
    CommonSetting.g_sMetaComm.sDBFileOption.bDBInitModem_2[nThreadID] = Enable;
}

QString Common::getAPDbPath_DUT() const
{
    return QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strAPDbPath_DUT);
}

void Common::setAPDbPath_DUT(QString str)
{
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strAPDbPath_DUT, "%s", str.toStdString().c_str());
}

QString Common::getMd1DbPath_DUT() const
{
    return QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strMD1DbPath_DUT);
}

void Common::setMd1DbPath_DUT(QString str)
{
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strMD1DbPath_DUT, "%s", str.toStdString().c_str());
}

QString Common::getMd2DbPath() const
{
    return QString("%1").arg(CommonSetting.g_sMetaComm.sDBFileOption.strMD2Dbpath);
}

void Common::setMd2DbPath(QString str)
{
    sprintf_s(CommonSetting.g_sMetaComm.sDBFileOption.strMD2Dbpath, "%s", str.toStdString().c_str());
}

//Test Count
int Common::getTestCount(int nThreadID, TestResult_Status m_status) const
{
    int num = 0;
    if(m_status == PASS)
        num = CommonSetting.m_TestCount[nThreadID].PassNums;
    else if(m_status == FAIL)
        num = CommonSetting.m_TestCount[nThreadID].FailNums;
    else
        num = CommonSetting.m_TestCount[nThreadID].CountNums;

    if(num < 0)
        return 0;
    else
        return num;
}

void Common::setTestCount(int nThreadID, TestResult_Status m_status)
{
    if(m_status == PASS)
        CommonSetting.m_TestCount[nThreadID].PassNums++;
    else if(m_status == FAIL)
        CommonSetting.m_TestCount[nThreadID].FailNums++;

    CommonSetting.m_TestCount[nThreadID].CountNums++;
}

void Common::ClearTestCount()
{
    for(int i=0;i<MAX_SUPPORT_COMPORT_NUM;i++)
    {
        CommonSetting.m_TestCount[i].PassNums = 0;
        CommonSetting.m_TestCount[i].FailNums = 0;
        CommonSetting.m_TestCount[i].CountNums = 0;
    }
}

bool Common::ResultToString_Win(DWORD ED, char* lpBuffer, DWORD nSize)
{
    DWORD ret_dw = 0u;
    DWORD i_dw = 0u, j_dw = 0u;

    if (lpBuffer == NULL || nSize < 64u)
        return false;

    ret_dw = FormatMessage(FORMAT_MESSAGE_FROM_SYSTEM|FORMAT_MESSAGE_IGNORE_INSERTS,
                           NULL,
                           ED,
                           MAKELANGID(LANG_ENGLISH, SUBLANG_ENGLISH_US),
                           lpBuffer,
                           nSize,
                           NULL);
    if (ret_dw == 0u)
        return false;

    // remove '\r' '\n'
    j_dw = 0u;
    for (i_dw = 0u; i_dw < ret_dw && lpBuffer[i_dw] != '\0'; i_dw++)
    {
        if (lpBuffer[i_dw] != '\r' && lpBuffer[i_dw] != '\n')
        {
            if (j_dw == i_dw)
                j_dw++;
            else
                lpBuffer[j_dw++] = lpBuffer[i_dw];
        }
    }
    lpBuffer[j_dw] = '\0';

    return true;
}

const char * Common::ResultToString_SP(int errCode)
{
    return META_GetErrorString((META_RESULT)errCode);
}

const char * Common::ResultToString(int errCode)
{
    return META_GetErrorString((META_RESULT)errCode);
}

void Common::DebugOnOff(int nThreadID, bool bOn)
{
    if (bOn && !bDebugOn[nThreadID])
    {
        int ret_i = 0;
        char log_file[MAX_PATH] = "";
        SYSTEMTIME time;
        m_hDebugTrace[nThreadID] = NULL;
        m_phDebugSentry[nThreadID] = NULL;

        // make sure log folder exist
        ret_i = ::PathFileExists(CommonSetting.m_LogDir);
        if (ret_i == FALSE)
        {
            ret_i = ::CreateDirectory(CommonSetting.m_LogDir, NULL);
            if (ret_i == FALSE)
            {
                if (strcmp(CommonSetting.m_LogDir, "C:\\Agenew_MultiCodes_Tool_log") == 0)
                {
                    QMessageBox::warning(nullptr, "Warning", "Log Dir not exist and can not be created ", QMessageBox::Yes, QMessageBox::Yes);
                    return;
                }

                QMessageBox::warning(nullptr, "Warning", "Log Dir not exist and can not be created.\r\nUse default C:\\Agenew_MultiCodes_Tool_log\\.", QMessageBox::Yes, QMessageBox::Yes);

                strcpy_s(CommonSetting.m_LogDir, "C:\\Agenew_MultiCodes_Tool_log");
                ret_i = ::CreateDirectory(CommonSetting.m_LogDir, NULL);
                if (ret_i == FALSE)
                {
                    QMessageBox::warning(nullptr, "Warning", "Can not create default log dir C:\\Agenew_MultiCodes_Tool_log\\", QMessageBox::Yes, QMessageBox::Yes);
                    return;
                }
            }
        }

        sprintf_s(CommonSetting.m_strLogDir_Sub[nThreadID], "%s\\%d",CommonSetting.m_LogDir, nThreadID+1);
        ret_i = ::PathFileExists(CommonSetting.m_strLogDir_Sub[nThreadID]);
        if (ret_i == FALSE)
        {
            ret_i = ::CreateDirectory(CommonSetting.m_strLogDir_Sub[nThreadID], NULL);
            if (ret_i == FALSE)
            {
                QMessageBox::warning(nullptr, "Warning", "Can not create default sub log dir.", QMessageBox::Yes, QMessageBox::Yes);
                CommonSetting.m_strLogDir_Sub[nThreadID][0] = '\0';
                return;
            }
        }

        ::GetLocalTime(&time);
        sprintf_s(CommonSetting.m_strLogDir_Sub[nThreadID], "%s\\%04u-%02u-%02u-%02u-%02u-%02u\\",
                  CommonSetting.m_strLogDir_Sub[nThreadID], time.wYear, time.wMonth, time.wDay, time.wHour, time.wMinute, time.wSecond);

        qDebug() << "Common::DebugOnOff: Create log dir path is " << CommonSetting.m_strLogDir_Sub[nThreadID];
        ret_i = ::CreateDirectory(CommonSetting.m_strLogDir_Sub[nThreadID], NULL);
        if (ret_i == FALSE)
        {
            QMessageBox::warning(nullptr, "Warning", "Can not create default sub log dir.", QMessageBox::Yes, QMessageBox::Yes);
            CommonSetting.m_strLogDir_Sub[nThreadID][0] = '\0';
            return;
        }

        m_phDebugSentry[nThreadID] = new MetaTrace_Handle_Sentry(m_hDebugTrace[nThreadID], "MultiATE", false, false);
        if(m_phDebugSentry[nThreadID] && !MTRACE_IsDumpFileOpen(m_hDebugTrace[nThreadID]))
        {
            sprintf_s(log_file, "%sAgenew_MultiCodes_Tool.log", CommonSetting.m_strLogDir_Sub[nThreadID]);
            if(MTRACE_DumpFileOpen(m_hDebugTrace[nThreadID], log_file, "w") == 0)
            {
                MTRACE_ON(m_hDebugTrace[nThreadID]);
            }
        }

        //_Xboot_DebugOn_With_FilePath(log_file);
        sprintf_s(log_file, "%sMetaCore.log", CommonSetting.m_strLogDir_Sub[nThreadID]);
        META_DebugOn_With_Handle_FilePath(nThreadID, log_file);
        sprintf_s(log_file, "%sSP_BROM_DLL.log", CommonSetting.m_strLogDir_Sub[nThreadID]);
        SP_Brom_Debug_SetLogFilename(log_file);
        SP_Brom_DebugOn();

        MTRACE(m_hDebugTrace[nThreadID], "%s", g_strToolVersion);
        bDebugOn[nThreadID] = true;
    }
    else if (!bOn && bDebugOn[nThreadID])
    {
        META_DebugOff_With_Handle(nThreadID);
        SP_Brom_DebugOff();
        //_Xboot_DebugOff();
        MTRACE_OFF(m_hDebugTrace[nThreadID]);

        memset(CommonSetting.m_strLogDir_Sub[nThreadID], 0, sizeof(CommonSetting.m_strLogDir_Sub[nThreadID]));
        qDebug() << "Common::DebugOnOff: close to record log! ";

        bDebugOn[nThreadID] = false;
    }
}

const char* Common::getSubLogDirPath(UINT nThreadID) const
{
    return CommonSetting.m_strLogDir_Sub[nThreadID];
}

bool Common::getMesOfflineEnable() const
{
    return CommonSetting.m_MesConfig.MesEnable;
}

void Common::setMesOfflineEnable(bool Enable)
{
    CommonSetting.m_MesConfig.MesEnable = Enable;
}

void Common::setMesNotCheckStationEnable(bool enable)
{
    CommonSetting.m_MesConfig.NotCheckStationEnbale = enable;
}

bool Common::getMesNotCheckStationEnable() const
{
    return CommonSetting.m_MesConfig.NotCheckStationEnbale;
}

void Common::setMesNotUpdateEnable(bool enable)
{
    CommonSetting.m_MesConfig.NotUpdateEnbale = enable;
}

bool Common::getMesNotUpdateEnable() const
{
    return CommonSetting.m_MesConfig.NotUpdateEnbale;
}

void Common::setMesType(QString str)
{
    CommonSetting.m_MesConfig.MesType = str;
}

QString Common::getMesType() const
{
    return CommonSetting.m_MesConfig.MesType;
}

void Common::setSDMesOrder(QString str)
{
    CommonSetting.m_MesConfig.SDConfig.Order = str;
}

QString Common::getSDMesOrder() const
{
    return CommonSetting.m_MesConfig.SDConfig.Order;
}

void Common::setSDMesStation(QString str)
{
    CommonSetting.m_MesConfig.SDConfig.Station = str;
}

QString Common::getSDMesStation() const
{
    return CommonSetting.m_MesConfig.SDConfig.Station;
}

void Common::setSDMesUserCode(QString str)
{
    CommonSetting.m_MesConfig.SDConfig.UserCode = str;
}

QString Common::getSDMesUserCode() const
{
    return CommonSetting.m_MesConfig.SDConfig.UserCode;
}

void Common::setAGNMesLine(QString str)
{
    CommonSetting.m_MesConfig.AGNConfig.Line = str;
}

QString Common::getAGNMesLine() const
{
    return CommonSetting.m_MesConfig.AGNConfig.Line;
}

void Common::setAGNMesOrder(QString str)
{
    CommonSetting.m_MesConfig.AGNConfig.Order = str;
}

QString Common::getAGNMesOrder() const
{
    return CommonSetting.m_MesConfig.AGNConfig.Order;
}

void Common::setAGNMesStation(QString str)
{
    CommonSetting.m_MesConfig.AGNConfig.Station = str;
}

QString Common::getAGNMesStation() const
{
    return CommonSetting.m_MesConfig.AGNConfig.Station;
}

void Common::setAGNMesServer(QString str)
{
    CommonSetting.m_MesConfig.AGNConfig.Server = str;
}

QString Common::getAGNMesServer() const
{
    return CommonSetting.m_MesConfig.AGNConfig.Server;
}

void Common::setAGNMesUserCode(QString str)
{
    CommonSetting.m_MesConfig.AGNConfig.UserCode = str;
}

QString Common::getAGNMesUserCode() const
{
    return CommonSetting.m_MesConfig.AGNConfig.UserCode;
}

void Common::setYDMesIP(QString str)
{
    CommonSetting.m_MesConfig.YDConfig.IP = str;
}

QString Common::getYDMesIP() const
{
    return CommonSetting.m_MesConfig.YDConfig.IP;
}

void Common::setYDMesOrder(QString str)
{
    CommonSetting.m_MesConfig.YDConfig.Order = str;
}

QString Common::getYDMesOrder() const
{
    return CommonSetting.m_MesConfig.YDConfig.Order;
}

void Common::setYDMesStation(QString str)
{
    CommonSetting.m_MesConfig.YDConfig.Station = str;
}

QString Common::getYDMesStation() const
{
    return CommonSetting.m_MesConfig.YDConfig.Station;
}

void Common::setYDMesResName(QString str)
{
    CommonSetting.m_MesConfig.YDConfig.ResName = str;
}

QString Common::getYDMesResName() const
{
    return CommonSetting.m_MesConfig.YDConfig.ResName;
}

//Meta Code
ScanData_struct Common::getScanData(int nThreadID) const
{
    return CommonSetting.g_sMetaComm.m_sScanData[nThreadID];
}

void Common::setScanData(int nThreadID, ScanData_struct data)
{
    CommonSetting.g_sMetaComm.m_sScanData[nThreadID] = data;
}

int Common::getIMEINum() const
{
    return CommonSetting.g_sMetaComm.sIMEIOption.iImeiNums;
}

void Common::setIMEINum(int num)
{
    CommonSetting.g_sMetaComm.sIMEIOption.iImeiNums = num;
}

QStringList Common::getTestItemOrder() const
{
    return m_testItemOrder;
}

void Common::setTestItemOrder(QStringList &order)
{
    m_testItemOrder = order;
}

/*
 * Testing Process Para
 */
bool Common::getSignalRet(int nThreadID) const
{
    return CommonSetting.m_RequestArg.SignalRet[nThreadID];
}

void Common::setSignalRet(int nThreadID, bool Enable)
{
    CommonSetting.m_RequestArg.SignalRet[nThreadID] = Enable;
}

void Common::setPortRequest(int nThreadID, QString str)
{
    CommonSetting.m_RequestArg.PortRequest[nThreadID] = str;
}

QString Common::getPortRequest(int nThreadID) const
{
    return CommonSetting.m_RequestArg.PortRequest[nThreadID];
}

void Common::setneedCtrBox(int nThreadID, int flag)
{
    CommonSetting.m_RequestArg.needCtrBox[nThreadID] = flag;
}

int Common::getneedCtrBox(int nThreadID) const
{
    return CommonSetting.m_RequestArg.needCtrBox[nThreadID];
}

bool Common::getCtrBoxFlag(int nThreadID) const
{
    return CommonSetting.m_RequestArg.CtrBoxFlag[nThreadID];
}

void Common::setCtrBoxFlag(int nThreadID, bool Enable)
{
    CommonSetting.m_RequestArg.CtrBoxFlag[nThreadID] = Enable;
}

void Common::setneedScanGun(int nThreadID, int flag)
{
    CommonSetting.m_RequestArg.needScanGun[nThreadID] = flag;
}

int Common::getneedScanGun(int nThreadID) const
{
    return CommonSetting.m_RequestArg.needScanGun[nThreadID];
}

bool Common::getScanGunFlag(int nThreadID) const
{
    return CommonSetting.m_RequestArg.CtrScanGunFlag[nThreadID];
}

void Common::setScanGunFlag(int nThreadID, bool Enable)
{
    CommonSetting.m_RequestArg.CtrScanGunFlag[nThreadID] = Enable;
}

bool Common::getTestBoxLastStatus(int nThreadID) const
{
    return m_BoxStatus.BoxLastStatus[nThreadID];
}

void Common::setTestBoxLastStatus(int nThreadID, bool Enable)
{
    m_BoxStatus.BoxLastStatus[nThreadID] = Enable;
}

void Common::setSleepFlag(bool ret)
{
    CommonSetting.m_RequestArg.Sleepflag = ret;
}

bool Common::getSleepFlag() const
{
    return CommonSetting.m_RequestArg.Sleepflag;
}

void Common::setScanGunSignal(int nThreadID, bool Enable)
{
    CommonSetting.m_RequestArg.ScanGunSignal[nThreadID] = Enable;
}

bool Common::getScanGunSignal(int nThreadID) const
{
    return CommonSetting.m_RequestArg.ScanGunSignal[nThreadID];
}

void Common::setScanGunRequest(int nThreadID, QString str)
{
    CommonSetting.m_RequestArg.ScanGunRequest[nThreadID] = str;
}

QString Common::getScanGunRequest(int nThreadID) const
{
    return CommonSetting.m_RequestArg.ScanGunRequest[nThreadID];
}

//Power
PowerType Common::getPowerType()
{
    return CommonSetting.iPowerType;
}

void Common::setPowerType(PowerType value)
{
    CommonSetting.iPowerType = value;
}

UsbControlType Common::getUsbControlType() const
{
    return CommonSetting.iUsbControlType;
}

void Common::setUsbControlType(UsbControlType value)
{
    CommonSetting.iUsbControlType = value;
}


/*
 * PowerOff Arg
 */
float Common::getCurrentMaxValue(CURRENTITEM_TYPE item) const
{
    float value = 0;
    QString testcase = "";
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    if(item == POWEROFF_ITEM)
        testcase = PowerOffCurrentItem::staticType();
    else if(item == POWERON_ITEM)
        testcase = PowerOnCurrentItem::staticType();
    else if(item == DEEPSLEEP_ITEM)
        testcase = DeepSleepCurrentItem::staticType();
    else if(item == CHARGE_ITEM)
        testcase = ChargeCurrentItem::staticType();
    else if(item == BOTTOM_ITEM)
        testcase = BottomCurrentItem::staticType();
    else
        return 0;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
        {
            QJsonObject paramObj = testCaseObj["params"].toObject();
            return paramObj["MaxCurrent"].toString().toFloat();
        }
    }

    if(value < 0)
        return 0;
    else
        return value;
}

float Common::getCurrentMinValue(CURRENTITEM_TYPE item) const
{
    float value = 0;
    QString testcase = "";
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    if(item == POWEROFF_ITEM)
        testcase = PowerOffCurrentItem::staticType();
    else if(item == POWERON_ITEM)
        testcase = PowerOnCurrentItem::staticType();
    else if(item == DEEPSLEEP_ITEM)
        testcase = DeepSleepCurrentItem::staticType();
    else if(item == CHARGE_ITEM)
        testcase = ChargeCurrentItem::staticType();
    else if(item == BOTTOM_ITEM)
        testcase = BottomCurrentItem::staticType();
    else
        return 0;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
        {
            QJsonObject paramObj = testCaseObj["params"].toObject();
            return paramObj["MinCurrent"].toString().toFloat();
        }
    }

    if(value < 0)
        return 0;
    else
        return value;
}

float Common::getCurrentTriggerValue(CURRENTITEM_TYPE item) const
{
    float value = 0;
    QString testcase = "";
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    if(item == POWEROFF_ITEM)
        testcase = PowerOffCurrentItem::staticType();
    else if(item == POWERON_ITEM)
        testcase = PowerOnCurrentItem::staticType();
    else if(item == DEEPSLEEP_ITEM)
        testcase = DeepSleepCurrentItem::staticType();
    else if(item == CHARGE_ITEM)
        testcase = ChargeCurrentItem::staticType();
    else if(item == BOTTOM_ITEM)
        testcase = BottomCurrentItem::staticType();
    else
        return 0;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
        {
            QJsonObject paramObj = testCaseObj["params"].toObject();
            return paramObj["TriggerCurrent"].toString().toFloat();
        }
    }

    if(value < 0)
        return 0;
    else
        return value;
}

int Common::getCurrentTestTime(CURRENTITEM_TYPE item) const
{
    int value = 0;
    QString testcase = "";
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    if(item == POWEROFF_ITEM)
        testcase = PowerOffCurrentItem::staticType();
    else if(item == POWERON_ITEM)
        testcase = PowerOnCurrentItem::staticType();
    else if(item == DEEPSLEEP_ITEM)
        testcase = DeepSleepCurrentItem::staticType();
    else if(item == CHARGE_ITEM)
        testcase = ChargeCurrentItem::staticType();
    else if(item == BOTTOM_ITEM)
        testcase = BottomCurrentItem::staticType();
    else
        return 0;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
        {
            QJsonObject paramObj = testCaseObj["params"].toObject();
            return paramObj["TestTime"].toString().toInt();
        }
    }

    if(value < 0)
        return 0;
    else
        return value;
}

int Common::getCurrentTimeout(CURRENTITEM_TYPE item) const
{
    int value = 0;
    QString testcase = "";
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    if(item == POWEROFF_ITEM)
        testcase = PowerOffCurrentItem::staticType();
    else if(item == POWERON_ITEM)
        testcase = PowerOnCurrentItem::staticType();
    else if(item == DEEPSLEEP_ITEM)
        testcase = DeepSleepCurrentItem::staticType();
    else if(item == CHARGE_ITEM)
        testcase = ChargeCurrentItem::staticType();
    else if(item == BOTTOM_ITEM)
        testcase = BottomCurrentItem::staticType();
    else
        return 0;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
        {
            QJsonObject paramObj = testCaseObj["params"].toObject();
            return paramObj["TriggerTimeout"].toString().toInt();
        }
    }

    if(value < 0)
        return 0;
    else
        return value;
}

int Common::getCurrentSleepTime(CURRENTITEM_TYPE item) const
{
    int value = 0;
    QString testcase = "";
    QJsonArray testCasesArray = CommonSetting.m_ItemJsonDoc.array();
    if(item == POWEROFF_ITEM)
        testcase = PowerOffCurrentItem::staticType();
    else if(item == POWERON_ITEM)
        testcase = PowerOnCurrentItem::staticType();
    else if(item == DEEPSLEEP_ITEM)
        testcase = DeepSleepCurrentItem::staticType();
    else if(item == CHARGE_ITEM)
        testcase = ChargeCurrentItem::staticType();
    else if(item == BOTTOM_ITEM)
        testcase = BottomCurrentItem::staticType();
    else
        return 0;

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(testcase == testCaseObj["type"].toString())
        {
            QJsonObject paramObj = testCaseObj["params"].toObject();
            return paramObj["Sleep"].toString().toInt();
        }
    }

    if(value < 0)
        return 0;
    else
        return value;
}

//Tee Old Version
bool Common::IsTeeOldVer() const
{
    return CommonSetting.g_sMetaComm.TeeOldVer;
}

void Common::SetTeeOldVer(bool Enable)
{
    CommonSetting.g_sMetaComm.TeeOldVer = Enable;
}

// TEE Version Management
QString Common::getCurrentTeeVersion() const
{
    return m_currentTeeVersion;
}

void Common::setCurrentTeeVersion(const QString& version)
{
    m_currentTeeVersion = version;
}

void Common::initializeTeeVersion(const QString& version)
{
    // Only set if not already initialized (empty)
    if (m_currentTeeVersion.isEmpty()) {
        m_currentTeeVersion = version;
        qDebug() << "TEE Version initialized to:" << version;
    }
}

bool Common::hasTeeVersionChanged()
{
    QJsonObject jsonObj = getJsonParams(TeeSupportItem::staticType());
    QString newTeeVersion = jsonObj["TEE Version"].toString();

    // Always compare with current stored version
    bool hasChanged = (newTeeVersion != m_currentTeeVersion);

    qDebug() << "TEE Version check - Current:" << m_currentTeeVersion << "New:" << newTeeVersion << "Changed:" << hasChanged;

    return hasChanged;
}

// TEE DLL Version Validation Functions
QString Common::getDllTeeVersion()
{
    try {
        // Get version from DLL
        // Note: This should only be called after KPHAProxy is initialized
        unsigned long versionCode = KPHAProxy::inst()->Dll_Get_KPHA_Version();
        QString versionStr = formatTeeVersion(versionCode);
        qDebug() << "DLL TEE Version code:" << versionCode << "formatted as:" << versionStr;
        return versionStr;
    } catch (...) {
        qDebug() << "Failed to get TEE DLL version, using default v5.7.3.1";
        return "v5.7.3.1";  // Fallback to default
    }
}

QString Common::formatTeeVersion(unsigned long versionCode)
{
    // Convert version code like 5731 to "v5.7.3.1"
    if (versionCode == 0) {
        return "v5.7.3.1";  // Default fallback
    }
    
    // For version codes like 5731 (v5.7.3.1)
    QString codeStr = QString::number(versionCode);
    
    // Handle different version code formats
    if (codeStr.length() >= 4) {
        // Generic format: XYZW -> vX.Y.Z.W
        QString major = codeStr.left(1);
        QString minor = codeStr.mid(1, 1);
        QString patch = codeStr.mid(2, 1);
        QString build = codeStr.mid(3, 1);
        return QString("v%1.%2.%3.%4").arg(major, minor, patch, build);
    }
    
    // Fallback to default
    return "v5.7.3.1";
}

bool Common::validateAndSyncTeeVersion()
{
    // Get current DLL version
    QString dllVersion = getDllTeeVersion();
    
    // Get config version
    QJsonObject jsonObj = getJsonParams(TeeSupportItem::staticType());
    QString configVersion = jsonObj["TEE Version"].toString();
    
    // Handle "Default" config value
    QString actualConfigVersion = (configVersion == "Default") ? "v5.7.3.1" : configVersion;
    
    qDebug() << "Version validation - DLL:" << dllVersion << "Config:" << actualConfigVersion;
    
    // Check if versions match
    if (dllVersion != actualConfigVersion) {
        qDebug() << "Version mismatch detected - DLL:" << dllVersion << "vs Config:" << actualConfigVersion;
        return false;  // Versions don't match
    }
    
    return true;  // Versions match
}

void Common::syncTeeVersionWithDll()
{
    // Get actual DLL version
    QString dllVersion = getDllTeeVersion();
    
    // Update config to match DLL version
    QJsonObject jsonObj = getJsonParams(TeeSupportItem::staticType());
    jsonObj["TEE Version"] = dllVersion;
    setJsonParams(TeeSupportItem::staticType(), jsonObj);
    
    // Update internal version tracking
    setCurrentTeeVersion(dllVersion);
    
    qDebug() << "TEE Version synchronized with DLL version:" << dllVersion;
}
