#ifndef TEESUPPORTITEM_H
#define TEESUPPORTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>
#include <QPushButton>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QSharedPointer>
#include <QLabel>

// 前向声明
class ModelUidReader;

class TeeSupportItem : public TestCaseBase
{
    Q_OBJECT
public:
    TeeSupportItem();

    QString type() const override { return "TeeSupport"; }
    static QString staticType() { return "TeeSupport"; }
    QString displayName() const override { return "Tee Support"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override;

    // 在保存后(写入INI后)调用，用于刷新TEE剩余状态
    void refreshTeeCount();

signals:
    void modelUidFileSelected(const QString& filePath);

private slots:
    void selectModelUidFile();
    void onModelSelectionChanged(const QString& model);
    void showFormatHelp();  // 显示格式帮助
    void updateTeeCount();  // 更新TEE剩余数量

private:
    void setupStyleSheet();
    void updateModelComboBox();
    void autoLocateSelectedModel();  // 自动定位保存的型号
    void updateModelSelectionInternal(const QString& model, bool emitSignal = true);  // 内部更新函数
    void clearModelSelection(bool emitSignal = true);  // 清空选择
    QString formatUidForDisplay(const QString& uid) const;  // 格式化UID显示
    int getTeeRemainingCount();  // 获取TEE剩余数量
    
    // 字符串常量
    static constexpr const char* PARAM_TEE_VERSION = "TEE Version";
    static constexpr const char* PARAM_MODEL_UID_FILE_PATH = "Model UID File Path";  
    static constexpr const char* PARAM_SELECTED_MODEL = "Selected Model";
    static constexpr const char* PARAM_SELECTED_UID = "Selected UID";
    static constexpr int MAX_UID_DISPLAY_LENGTH = 24;
    
    // UI 常量
    static constexpr int HELP_BUTTON_SIZE = 28;
    static constexpr const char* HELP_BUTTON_ICON = "?";
    
private:
    QSharedPointer<ModelUidReader> m_modelUidReader;
    QLineEdit* m_modelUidFileLineEdit;
    QComboBox* m_modelComboBox;
    QLabel* m_teeCountLabel;  // TEE剩余数量显示标签
    
    QString m_styleSheet = R"(
        QGroupBox {
            border: 0.5px solid;
            border-radius: 3px;
            padding: 3px;
            margin-top: 2.2ex;
        }
        QGroupBox::title {
            subcontrol-position: top left;
            left: 12px;
            top: -8px;
        }
    )";
};

#endif // TEESUPPORTITEM_H
