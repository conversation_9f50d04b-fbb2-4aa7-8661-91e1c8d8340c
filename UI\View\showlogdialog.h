#ifndef SHOWLOGDIALOG_H
#define SHOWLOGDIALOG_H

#include <QDialog>
#include <QEvent>

namespace Ui {
class ShowLogDialog;
}

class ShowLogDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ShowLogDialog(QWidget *parent = nullptr);
    ~ShowLogDialog();

    void InitLogText(QString Text);

protected:
    void changeEvent(QEvent* event) override;

private:
    Ui::ShowLogDialog *ui;
};

#endif // SHOWLOGDIALOG_H
