#ifndef INIFILEHANDLER_H
#define INIFILEHANDLER_H

#include <QString>
#include <QSettings>

class IniFileHandler
{
public:
    IniFileHandler();
    ~IniFileHandler();
    
    // 写入UID到kph_env.ini文件的[vturkey]keybox_uuid项
    static bool writeUidToKphEnvIni(const QString& uid, QString& errorInfo);
    
    // 获取exe目录路径
    static QString getExeDirectory();
    
    // 获取kph_env.ini文件的完整路径
    static QString getKphEnvIniPath();
    
    // 检查kph_env.ini文件是否存在
    static bool checkKphEnvIniExists();
    
    // 读取当前的keybox_uuid值
    static QString getCurrentKeyboxUuid();

private:
    // 验证UID格式是否正确
    static bool validateUidFormat(const QString& uid);
    
    // 创建目录如果不存在
    static bool ensureDirectoryExists(const QString& dirPath);
    
    // 创建默认的kph_env.ini文件
    static bool createDefaultKphEnvIni(const QString& filePath, QString& errorInfo);
};

#endif // INIFILEHANDLER_H