#include "TeeSupportItem.h"
#include "../../../Utility/ModelUidReader.h"
#include "../../../Utility/IniFileHandler.h"
#include "../../../3rdParty/KPHA-SDK-v5731/KPHAProxy.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QDebug>

TeeSupportItem::TeeSupportItem()
    : m_modelUidReader(new ModelUidReader())
    , m_modelUidFileLineEdit(nullptr)
    , m_modelComboBox(nullptr)
    , m_teeCountLabel(nullptr)
{
    m_params = {
        {PARAM_TEE_VERSION, "Default"},
        {PARAM_MODEL_UID_FILE_PATH, ""},
        {PARAM_SELECTED_MODEL, ""},
        {PARAM_SELECTED_UID, ""}
    };
}

void TeeSupportItem::setParameters(const QVariantMap& params)
{
    m_params = params;
}

QWidget* TeeSupportItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    // TEE版本选择
    QComboBox* TeeCombo = new QComboBox(widget);
    TeeCombo->addItems({"Default", "v4.3.6.3", "v4.5.0.0", "v4.6.1.0", "v5.7.3.1"});
    TeeCombo->setCurrentText(m_params[PARAM_TEE_VERSION].toString());
    connect(TeeCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params[PARAM_TEE_VERSION] = text;
        emit parametersChanged();
    });

    // 文件选择控件
    QHBoxLayout* fileSelectLayout = new QHBoxLayout();
    m_modelUidFileLineEdit = new QLineEdit(widget);
    m_modelUidFileLineEdit->setText(m_params[PARAM_MODEL_UID_FILE_PATH].toString());
    m_modelUidFileLineEdit->setReadOnly(true);
    m_modelUidFileLineEdit->setPlaceholderText(tr("Please select an Excel file containing Model and UID"));

    // 设置工具提示，说明Excel格式要求
    m_modelUidFileLineEdit->setToolTip(
        tr("Excel requirements for selecting Model and UID from table:\n"
           "• Contains header row\n"
           "• Model column + UID column\n"
           "• Supports .xlsx/.xls\n"
           "• Click ? button for details")
    );

    QPushButton* selectFileButton = new QPushButton(tr("Select File..."), widget);
    connect(selectFileButton, &QPushButton::clicked, this, &TeeSupportItem::selectModelUidFile);

    // 添加格式帮助按钮
    QPushButton* helpButton = new QPushButton(HELP_BUTTON_ICON, widget);
    helpButton->setFixedSize(HELP_BUTTON_SIZE, HELP_BUTTON_SIZE);
    helpButton->setToolTip(tr("Click to view detailed Excel format instructions"));
    helpButton->setStyleSheet(R"(
        QPushButton {
            border: 1px solid #cccccc;
            border-radius: 14px;
            font-size: 12pt;
            font-weight: bold;
            background-color: #f8f9fa;
        }
        QPushButton:hover {
            background-color: #e9ecef;
            border-color: #007bff;
        }
        QPushButton:pressed {
            background-color: #dee2e6;
        }
    )");
    connect(helpButton, &QPushButton::clicked, this, &TeeSupportItem::showFormatHelp);

    fileSelectLayout->addWidget(m_modelUidFileLineEdit);
    fileSelectLayout->addWidget(selectFileButton);
    fileSelectLayout->addWidget(helpButton);

    // 型号选择下拉框
    m_modelComboBox = new QComboBox(widget);
    m_modelComboBox->setEnabled(false);
    connect(m_modelComboBox, &QComboBox::currentTextChanged,
            this, &TeeSupportItem::onModelSelectionChanged);

    // TEE剩余数量显示
    m_teeCountLabel = new QLabel(tr("TEE Remaining: Checking..."), widget);
    m_teeCountLabel->setStyleSheet(
        "QLabel {"
        "    color: #666666;"
        "    font-size: 9pt;"
        "    padding: 2px 5px;"
        "    border: 1px solid #cccccc;"
        "    border-radius: 3px;"
        "    background-color: #f8f9fa;"
        "}");
    m_teeCountLabel->setMinimumHeight(22);
    m_teeCountLabel->setAlignment(Qt::AlignCenter);
    updateTeeCount();  // 初始化时更新一次

    // 添加到主布局
    layout->addRow(tr("TEE Ver:"), TeeCombo);
    layout->addRow(tr("Config File:"), fileSelectLayout);
    layout->addRow(tr("Select Model:"), m_modelComboBox);
    layout->addRow(tr("TEE Status:"), m_teeCountLabel);

    // 如果已有文件路径，尝试加载并自动定位型号
    QString existingFile = m_params[PARAM_MODEL_UID_FILE_PATH].toString();

    if (!existingFile.isEmpty() && QFile::exists(existingFile)) {
        QString errorInfo;

        if (m_modelUidReader->loadExcelFile(existingFile, errorInfo)) {
            updateModelComboBox();
            // 自动定位保存的型号
            autoLocateSelectedModel();

            // 如果有选中的型号和UID，自动应用配置
            QString selectedModel = m_params[PARAM_SELECTED_MODEL].toString();
            QString selectedUid = m_params[PARAM_SELECTED_UID].toString();
            if (!selectedModel.isEmpty() && !selectedUid.isEmpty()) {
                qDebug() << "Model selection loaded and saved, pending INI write on Save:" << selectedModel;
            }
        }
    }

    // 初始化TEE状态显示
    updateTeeCount();

    return widget;
}

void TeeSupportItem::selectModelUidFile()
{
    QString filePath = QFileDialog::getOpenFileName(
        nullptr,
        tr("Select Model-UID Config File"),
        "",
        tr("Excel Files (*.xlsx *.xls);;All Files (*.*)")
    );

    if (!filePath.isEmpty()) {
        QString errorInfo;
        if (m_modelUidReader->loadExcelFile(filePath, errorInfo)) {
            m_modelUidFileLineEdit->setText(filePath);
            m_params[PARAM_MODEL_UID_FILE_PATH] = filePath;
            updateModelComboBox();
            emit parametersChanged();
            emit modelUidFileSelected(filePath);

            QMessageBox::information(nullptr, tr("Success"),
                tr("Excel file loaded successfully, found %1 models").arg(m_modelUidReader->getDataCount()));
        } else {
            QMessageBox::critical(nullptr, tr("Error"), tr("Failed to load Excel file:\n%1").arg(errorInfo));
        }
    }
}

void TeeSupportItem::updateModelComboBox()
{
    // 阻塞信号，防止clear()触发onModelSelectionChanged清空参数
    m_modelComboBox->blockSignals(true);
    m_modelComboBox->clear();
    m_modelComboBox->blockSignals(false);

    if (!m_modelUidReader->isLoaded()) {
        m_modelComboBox->setEnabled(false);
        return;
    }

    QStringList models = m_modelUidReader->getModelList();
    if (models.isEmpty()) {
        m_modelComboBox->addItem(tr("No model data found"));
        m_modelComboBox->setEnabled(false);
        return;
    }

    // 阻塞信号，防止addItems()触发意外的信号
    m_modelComboBox->blockSignals(true);
    m_modelComboBox->addItems(models);
    m_modelComboBox->setEnabled(true);
    m_modelComboBox->blockSignals(false);
}

void TeeSupportItem::autoLocateSelectedModel()
{
    QString savedModel = m_params[PARAM_SELECTED_MODEL].toString();
    QString savedUid = m_params[PARAM_SELECTED_UID].toString();

    if (savedModel.isEmpty()) {
        return; // 没有保存的型号，无需定位
    }

    if (!m_modelUidReader || !m_modelUidReader->isLoaded()) {
        return;
    }

    QStringList models = m_modelUidReader->getModelList();

    // 检查保存的型号是否在表格中存在
    if (models.contains(savedModel)) {
        // 型号存在，检查UID是否匹配
        QString currentUid = m_modelUidReader->getUidByModel(savedModel);

        if (currentUid == savedUid) {
            // 型号和UID都匹配，自动定位

            // 阻止信号触发避免递归调用
            m_modelComboBox->blockSignals(true);
            m_modelComboBox->setCurrentText(savedModel);
            m_modelComboBox->blockSignals(false);

            // 手动触发选择变化，但不发出parametersChanged信号（因为是自动定位，不是用户修改）
            updateModelSelectionInternal(savedModel, false);
            return;
        } else {
            // 型号存在但UID不匹配，提示用户
            QMessageBox::warning(nullptr, tr("Configuration Reminder"),
                tr("Model '%1' exists in the table, but the corresponding UID has changed.\n\n"
                        "Please reselect the model and apply the configuration.")
                    .arg(savedModel));
        }
    } else {
        // 型号在表格中不存在，提示用户并清空选择
        QMessageBox::warning(nullptr, tr("Configuration Reminder"),
            tr("The previously selected model '%1' does not exist in the current table.\n\n"
                    "Please reselect the appropriate model.")
                .arg(savedModel));
    }

    // 清空之前保存的选择
    clearModelSelection(false);  // 使用辅助函数，不触发信号

    m_modelComboBox->blockSignals(true);
    m_modelComboBox->setCurrentIndex(-1); // 显示空白
    m_modelComboBox->blockSignals(false);

    // 使用内部函数更新UI状态，但不触发parametersChanged信号
    updateModelSelectionInternal("", false);
}

void TeeSupportItem::onModelSelectionChanged(const QString& model)
{
    updateModelSelectionInternal(model, true);  // 用户操作，触发信号
}

void TeeSupportItem::updateModelSelectionInternal(const QString& model, bool emitSignal)
{
    if (model.isEmpty() || !m_modelUidReader->isLoaded()) {
        clearModelSelection(emitSignal);
        return;
    }

    QString uid = m_modelUidReader->getUidByModel(model);
    if (uid.isEmpty()) {
        clearModelSelection(emitSignal);
        return;
    }

    m_params[PARAM_SELECTED_MODEL] = model;
    m_params[PARAM_SELECTED_UID] = uid;

    if (emitSignal) {
        emit parametersChanged();
    }
}

void TeeSupportItem::clearModelSelection(bool emitSignal)
{
    m_params[PARAM_SELECTED_MODEL] = "";
    m_params[PARAM_SELECTED_UID] = "";

    if (emitSignal) {
        emit parametersChanged();
    }
}

QString TeeSupportItem::formatUidForDisplay(const QString& uid) const
{
    if (uid.length() <= MAX_UID_DISPLAY_LENGTH) {
        return uid;
    }
    return uid.left(8) + "..." + uid.right(8);
}

void TeeSupportItem::showFormatHelp()
{
    QMessageBox msgBox;
    msgBox.setWindowTitle(tr("Excel File Format Instructions"));
    msgBox.setIcon(QMessageBox::Information);

    msgBox.setText(
        tr("<h3 style='color: #2E86AB;'>Excel file format requirements for selecting Model UID</h3>"
           "<p><b>File formats:</b> Supports .xlsx and .xls</p>"
           "<p><b>Table Structure:</b></p>"
           "<ul style='margin-left: 20px;'>"
           "<li><b>Must include a header row</b> (first row is the header)</li>"
           "<li>Model column titles: <code>Model</code> / <code>型号</code> / <code>model</code> / <code>MODEL</code></li>"
           "<li>UID column titles: <code>UID</code> / <code>uid</code> / <code>UUID</code> / <code>uuid</code></li>"
           "</ul>")
    );

    msgBox.setDetailedText(
        tr("Excel file example:\n\n"
           "Row 1 (Header):\n"
           "Column A     Column B\n"
           "Model        UID\n\n"
           "Row 2 and later (Data rows):\n"
           "K3-P         8960_5cfbc611f8c3fe18f325a1f74a...\n"
        "H1502RQ     10244_00000000000000000000000000...\n"
        "X50-Pro     9556_581022f7afecdf3cabb6d4b912...\n\n"
        "支持的列名（不区分大小写）：\n"
        "• Model列：Model, 型号, model, MODEL\n"
        "• UID列：UID, uid, UUID, uuid\n\n"
        "注意事项：\n"
        "• 第一行必须是表头，程序会自动识别列名\n"
        "• Model列不能为空，且不能重复\n"
        "• UID列不能为空，建议使用完整的标识符\n"
        "• 文件大小建议不超过50MB\n"
        "• 确保文件没有被其他程序占用"
    ));

    msgBox.exec();
}

int TeeSupportItem::getTeeRemainingCount()
{
    // 获取当前选中的UID
    QString currentUid = m_params[PARAM_SELECTED_UID].toString();

    if (currentUid.isEmpty()) {
        return -1; // UID为空，无法检查许可证数量
    }

    // 获取KPHAProxy实例
    KPHAProxy* proxy = KPHAProxy::inst();
    if (!proxy) {
        qDebug() << "Failed to get KPHAProxy instance";
        return -2;
    }

    // 初始化KPHA环境
    char kph_base_path[256] = {0};
    char module_path[MAX_PATH] = {0};
    GetModuleFileName(NULL, module_path, MAX_PATH);
    std::string::size_type pos = std::string(module_path).find_last_of("/\\");
    sprintf(kph_base_path, "%s\\tee_stuff", std::string(module_path).substr(0, pos).c_str());

    int initResult = proxy->Dll_KPHA_InitEnv(kph_base_path);
    if (initResult != 0) {
        qDebug() << "KPHA_InitEnv failed with code:" << initResult;
        return -2;
    }

    // 获取许可证数量
    int licenseCount = proxy->Dll_KPHA_GetLicenseCount();

    // 清理环境
    proxy->Dll_KPHA_ClearEnv();

    return licenseCount;
}

void TeeSupportItem::updateTeeCount()
{
    if (!m_teeCountLabel) {
        return;
    }

    int count = getTeeRemainingCount();

    if (count >= 0) {
        m_teeCountLabel->setText(tr("TEE Remaining: %1").arg(count));

        // 根据数量设置颜色
        if (count > 10) {
            m_teeCountLabel->setStyleSheet(
                "QLabel {"
                "    color: #28a745;"
                "    font-size: 9pt;"
                "    font-weight: bold;"
                "    padding: 2px 5px;"
                "    border: 1px solid #28a745;"
                "    border-radius: 3px;"
                "    background-color: #d4edda;"
                "}");
        } else if (count > 0) {
            m_teeCountLabel->setStyleSheet(
                "QLabel {"
                "    color: #ffc107;"
                "    font-size: 9pt;"
                "    font-weight: bold;"
                "    padding: 2px 5px;"
                "    border: 1px solid #ffc107;"
                "    border-radius: 3px;"
                "    background-color: #fff3cd;"
                "}");
        } else {
            m_teeCountLabel->setText(tr("TEE Remaining: 0 (Exhausted)"));
            m_teeCountLabel->setStyleSheet(
                "QLabel {"
                "    color: #dc3545;"
                "    font-size: 9pt;"
                "    font-weight: bold;"
                "    padding: 2px 5px;"
                "    border: 1px solid #dc3545;"
                "    border-radius: 3px;"
                "    background-color: #f8d7da;"
                "}");
        }
    } else if (count == -1) {
        m_teeCountLabel->setText(tr("TEE Remaining: No model selected"));
        m_teeCountLabel->setStyleSheet(
            "QLabel {"
            "    color: #666666;"
            "    font-size: 9pt;"
            "    padding: 2px 5px;"
            "    border: 1px solid #cccccc;"
            "    border-radius: 3px;"
            "    background-color: #f8f9fa;"
            "}");
    } else {
        m_teeCountLabel->setText(tr("TEE Remaining: Check failed"));
        m_teeCountLabel->setStyleSheet(
            "QLabel {"
            "    color: #dc3545;"
            "    font-size: 9pt;"
            "    padding: 2px 5px;"
            "    border: 1px solid #dc3545;"
            "    border-radius: 3px;"
            "    background-color: #f8d7da;"
            "}");
    }
}

void TeeSupportItem::refreshTeeCount()
{
    // 在INI更新后调用，重新读取剩余量
    updateTeeCount();
}
