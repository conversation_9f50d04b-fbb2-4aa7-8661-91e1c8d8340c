#include "InputCodeItem.h"
#include <QFileDialog>

InputCodeItem::InputCodeItem()
{
    m_params = {
        {"XlsxFilePath", ""},
        {"YNCode", ""},
        {"SN", true},
        {"SN From", "Input"},
        {"IMEI1", true},
        {"IMEI1 From", "Input"},
        {"IMEI2", true},
        {"IMEI2 From", "Input"},
        {"MEID", true},
        {"MEID From", "Input"},
        {"BtMac", true},
        {"BtMac From", "Input"},
        {"BtMac Prefix", "000CE7"},
        {"BtMac SectionStart", "000000"},
        {"BtMac SectionEnd", "FFFFFE"},
        {"BtMac SectionNext", "000CE7000000"},
        {"BtMac SectionStep", "2"},
        {"WifiMac", true},
        {"WifiMac From", "Input"},
        {"WifiMac Prefix", "000CE7"},
        {"WifiMac SectionStart", "000001"},
        {"WifiMac SectionEnd", "FFFFFF"},
        {"WifiMac SectionNext", "000CE7000001"},
        {"WifiMac SectionStep", "2"},
        {"EthernetMac", true},
        {"EthernetMac From", "Input"},
        {"SerialNo", true},
        {"SerialNo From", "Input"},
        {"NetCode", true},
        {"NetCode From", "Input"},
    };
}

QWidget* InputCodeItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    // xlsx
    QHBoxLayout* xlsxLayout = new QHBoxLayout();
    m_xlsxPathLineEdit = new QLineEdit(widget);
    m_xlsxPathLineEdit->setText(m_params["XlsxFilePath"].toString());
    m_xlsxPathLineEdit->setReadOnly(true);
    
    QPushButton* selectXlsxButton = new QPushButton(tr("View..."), widget);
    connect(selectXlsxButton, &QPushButton::clicked, this, &InputCodeItem::selectXlsxFile);
    xlsxLayout->addWidget(m_xlsxPathLineEdit);
    xlsxLayout->addWidget(selectXlsxButton);

    QLineEdit* YNCode = new QLineEdit(widget);
    YNCode->setText(m_params["YNCode"].toString());
    YNCode->setValidator(barcodeValidator);
    connect(YNCode, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["YNCode"] = text;
        emit parametersChanged();
    });

    QGroupBox* CommonGroup = new QGroupBox(tr("Common"), widget);
    CommonGroup->setStyleSheet(styleSheet);
    QFormLayout* CommonLayout = new QFormLayout(CommonGroup);
    CommonGroup->setLayout(CommonLayout);
    CommonLayout->addRow(tr("Xlsx File: "), xlsxLayout);
    CommonLayout->addRow(tr("YN Code: "), YNCode);
    layout->addRow(CommonGroup);


    //SN
    QCheckBox* SNEnable = new QCheckBox(tr("Enable"), widget);
    SNEnable->setChecked(m_params["SN"].toBool());
    connect(SNEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["SN"] = checked;
        emit parametersChanged();
    });

    QComboBox* SNCombo = new QComboBox(widget);
    SNCombo->addItems({tr("Input")});
    SNCombo->setCurrentText(m_params["SN From"].toString());
    connect(SNCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["SN From"] = text;
        emit parametersChanged();
    });

    QGroupBox* SNGroup = new QGroupBox("SN", widget);
    SNGroup->setStyleSheet(styleSheet);
    QFormLayout* SNLayout = new QFormLayout(SNGroup);
    SNGroup->setLayout(SNLayout);
    SNLayout->addRow(tr("SN: "), SNEnable);
    SNLayout->addRow(tr("SN From:"), SNCombo);
    layout->addRow(SNGroup);


    //IMEI1
    QCheckBox* IMEI1Enable = new QCheckBox(tr("Enable"), widget);
    IMEI1Enable->setChecked(m_params["IMEI1"].toBool());
    connect(IMEI1Enable, &QCheckBox::toggled, [this](bool checked) {
        m_params["IMEI1"] = checked;
        emit parametersChanged();
    });

    QComboBox* IMEI1Combo = new QComboBox(widget);
    IMEI1Combo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile"), tr("Default")});
    IMEI1Combo->setCurrentText(m_params["IMEI1 From"].toString());
    connect(IMEI1Combo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["IMEI1 From"] = text;
        emit parametersChanged();
    });

    QGroupBox* IMEIGroup = new QGroupBox("IMEI1", widget);
    IMEIGroup->setStyleSheet(styleSheet);
    QFormLayout* IMEILayout = new QFormLayout(IMEIGroup);
    IMEIGroup->setLayout(IMEILayout);
    IMEILayout->addRow(tr("IMEI1: "), IMEI1Enable);
    IMEILayout->addRow(tr("IMEI1 From:"), IMEI1Combo);
    layout->addRow(IMEIGroup);


    //IMEI2
    QCheckBox* IMEI2Enable = new QCheckBox(tr("Enable"), widget);
    IMEI2Enable->setChecked(m_params["IMEI2"].toBool());
    connect(IMEI2Enable, &QCheckBox::toggled, [this](bool checked) {
        m_params["IMEI2"] = checked;
        emit parametersChanged();
    });

    QComboBox* IMEI2Combo = new QComboBox(widget);
    IMEI2Combo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile"), tr("Default")});
    IMEI2Combo->setCurrentText(m_params["IMEI2 From"].toString());
    connect(IMEI2Combo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["IMEI2 From"] = text;
        emit parametersChanged();
    });

    QGroupBox* IMEI2Group = new QGroupBox("IMEI2", widget);
    IMEI2Group->setStyleSheet(styleSheet);
    QFormLayout* IMEI2Layout = new QFormLayout(IMEI2Group);
    IMEI2Group->setLayout(IMEI2Layout);
    IMEI2Layout->addRow(tr("IMEI2: "), IMEI2Enable);
    IMEI2Layout->addRow(tr("IMEI2 From:"), IMEI2Combo);
    layout->addRow(IMEI2Group);


    //MEID
    QCheckBox* MEIDEnable = new QCheckBox(tr("Enable"), widget);
    MEIDEnable->setChecked(m_params["MEID"].toBool());
    connect(MEIDEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["MEID"] = checked;
        emit parametersChanged();
    });

    QComboBox* MEIDCombo = new QComboBox(widget);
    MEIDCombo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile"), tr("Default")});
    MEIDCombo->setCurrentText(m_params["MEID From"].toString());
    connect(MEIDCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["MEID From"] = text;
        emit parametersChanged();
    });

    QGroupBox* MEIDGroup = new QGroupBox("MEID", widget);
    MEIDGroup->setStyleSheet(styleSheet);
    QFormLayout* MEIDLayout = new QFormLayout(MEIDGroup);
    MEIDGroup->setLayout(MEIDLayout);
    MEIDLayout->addRow(tr("MEID: "), MEIDEnable);
    MEIDLayout->addRow(tr("MEID From:"), MEIDCombo);
    layout->addRow(MEIDGroup);


    //BtMac
    QCheckBox* BtMacEnable = new QCheckBox(tr("Enable"), widget);
    BtMacEnable->setChecked(m_params["BtMac"].toBool());
    connect(BtMacEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["BtMac"] = checked;
        emit parametersChanged();
    });

    QComboBox* BtMacCombo = new QComboBox(widget);
    BtMacCombo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile"), tr("Section"), tr("YNSystem")});
    BtMacCombo->setCurrentText(m_params["BtMac From"].toString());
    connect(BtMacCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["BtMac From"] = text;
        emit parametersChanged();
    });

    QLineEdit* BtMacPrefix = new QLineEdit(widget);
    BtMacPrefix->setText(m_params["BtMac Prefix"].toString());
    BtMacPrefix->setValidator(btValidator);
    connect(BtMacPrefix, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BtMac Prefix"] = text;
        emit parametersChanged();
    });

    QLineEdit* BtMacSectionStart = new QLineEdit(widget);
    BtMacSectionStart->setText(m_params["BtMac SectionStart"].toString());
    BtMacSectionStart->setValidator(btValidator);
    connect(BtMacSectionStart, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BtMac SectionStart"] = text;
        emit parametersChanged();
    });

    QLineEdit* BtMacSectionEnd = new QLineEdit(widget);
    BtMacSectionEnd->setText(m_params["BtMac SectionEnd"].toString());
    BtMacSectionEnd->setValidator(btValidator);
    connect(BtMacSectionEnd, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BtMac SectionEnd"] = text;
        emit parametersChanged();
    });

    QLineEdit* BtMacSectionNext = new QLineEdit(widget);
    BtMacSectionNext->setText(m_params["BtMac SectionNext"].toString());
    BtMacSectionNext->setValidator(btValidator);
    connect(BtMacSectionNext, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BtMac SectionNext"] = text;
        emit parametersChanged();
    });

    QLineEdit* BtMacSectionStep = new QLineEdit(widget);
    BtMacSectionStep->setText(m_params["BtMac SectionStep"].toString());
    BtMacSectionStep->setValidator(btValidator);
    connect(BtMacSectionStep, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BtMac SectionStep"] = text;
        emit parametersChanged();
    });

    QGroupBox* BtMacGroup = new QGroupBox("BtMac", widget);
    BtMacGroup->setStyleSheet(styleSheet);
    QFormLayout* BtMacLayout = new QFormLayout(BtMacGroup);
    BtMacGroup->setLayout(BtMacLayout);
    BtMacLayout->addRow(tr("BtMac: "), BtMacEnable);
    BtMacLayout->addRow(tr("BtMac From:"), BtMacCombo);
    BtMacLayout->addRow(tr("BtMac Prefix: "), BtMacPrefix);
    BtMacLayout->addRow(tr("BtMac Section Start: "), BtMacSectionStart);
    BtMacLayout->addRow(tr("BtMac Section End: "), BtMacSectionEnd);
    BtMacLayout->addRow(tr("BtMac Section Next: "), BtMacSectionNext);
    BtMacLayout->addRow(tr("BtMac Section Step: "), BtMacSectionStep);
    layout->addRow(BtMacGroup);


    //WifiMac
    QCheckBox* WifiMacEnable = new QCheckBox(tr("Enable"), widget);
    WifiMacEnable->setChecked(m_params["WifiMac"].toBool());
    connect(WifiMacEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["WifiMac"] = checked;
        emit parametersChanged();
    });

    QComboBox* WifiMacCombo = new QComboBox(widget);
    WifiMacCombo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile"), tr("Section"), tr("YnSystem")});
    WifiMacCombo->setCurrentText(m_params["WifiMac From"].toString());
    connect(WifiMacCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["WifiMac From"] = text;
        emit parametersChanged();
    });

    QLineEdit* WifiMacPrefix = new QLineEdit(widget);
    WifiMacPrefix->setText(m_params["WifiMac Prefix"].toString());
    WifiMacPrefix->setValidator(wifiValidator);
    connect(WifiMacPrefix, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["WifiMac Prefix"] = text;
        emit parametersChanged();
    });

    QLineEdit* WifiMacSectionStart = new QLineEdit(widget);
    WifiMacSectionStart->setText(m_params["WifiMac SectionStart"].toString());
    WifiMacSectionStart->setValidator(wifiValidator);
    connect(WifiMacSectionStart, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["WifiMac SectionStart"] = text;
        emit parametersChanged();
    });

    QLineEdit* WifiMacSectionEnd = new QLineEdit(widget);
    WifiMacSectionEnd->setText(m_params["WifiMac SectionEnd"].toString());
    WifiMacSectionEnd->setValidator(wifiValidator);
    connect(WifiMacSectionEnd, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["WifiMac SectionEnd"] = text;
        emit parametersChanged();
    });

    QLineEdit* WifiMacSectionNext = new QLineEdit(widget);
    WifiMacSectionNext->setText(m_params["WifiMac SectionNext"].toString());
    WifiMacSectionNext->setValidator(btValidator);
    connect(WifiMacSectionNext, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["WifiMac SectionNext"] = text;
        emit parametersChanged();
    });

    QLineEdit* WifiMacSectionStep = new QLineEdit(widget);
    WifiMacSectionStep->setText(m_params["WifiMac SectionStep"].toString());
    WifiMacSectionStep->setValidator(btValidator);
    connect(WifiMacSectionStep, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["WifiMac SectionStep"] = text;
        emit parametersChanged();
    });

    QGroupBox* WifiMacGroup = new QGroupBox("WifiMac", widget);
    WifiMacGroup->setStyleSheet(styleSheet);
    QFormLayout* WifiMacLayout = new QFormLayout(WifiMacGroup);
    WifiMacGroup->setLayout(WifiMacLayout);
    WifiMacLayout->addRow(tr("WifiMac: "), WifiMacEnable);
    WifiMacLayout->addRow(tr("WifiMac From:"), WifiMacCombo);
    WifiMacLayout->addRow(tr("WifiMac Prefix: "), WifiMacPrefix);
    WifiMacLayout->addRow(tr("WifiMac Section Start: "), WifiMacSectionStart);
    WifiMacLayout->addRow(tr("WifiMac Section End: "), WifiMacSectionEnd);
    WifiMacLayout->addRow(tr("WifiMac Section Next: "), WifiMacSectionNext);
    WifiMacLayout->addRow(tr("WifiMac Section Step: "), WifiMacSectionStep);
    layout->addRow(WifiMacGroup);


    //EthernetMac
    QCheckBox* EthernetMacEnable = new QCheckBox(tr("Enable"), widget);
    EthernetMacEnable->setChecked(m_params["EthernetMac"].toBool());
    connect(EthernetMacEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["EthernetMac"] = checked;
        emit parametersChanged();
    });

    QComboBox* EthernetMacCombo = new QComboBox(widget);
    EthernetMacCombo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile")});
    EthernetMacCombo->setCurrentText(m_params["EthernetMac From"].toString());
    connect(EthernetMacCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["EthernetMac From"] = text;
        emit parametersChanged();
    });

    QGroupBox* EthernetMacGroup = new QGroupBox("EthernetMac", widget);
    EthernetMacGroup->setStyleSheet(styleSheet);
    QFormLayout* EthernetMacLayout = new QFormLayout(EthernetMacGroup);
    EthernetMacGroup->setLayout(EthernetMacLayout);
    EthernetMacLayout->addRow(tr("EthernetMac: "), EthernetMacEnable);
    EthernetMacLayout->addRow(tr("EthernetMac From:"), EthernetMacCombo);
    layout->addRow(EthernetMacGroup);


    //SerialNo
    QCheckBox* SerialNoEnable = new QCheckBox(tr("Enable"), widget);
    SerialNoEnable->setChecked(m_params["SerialNo"].toBool());
    connect(SerialNoEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["SerialNo"] = checked;
        emit parametersChanged();
    });

    QComboBox* SerialNoCombo = new QComboBox(widget);
    SerialNoCombo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile")});
    SerialNoCombo->setCurrentText(m_params["SerialNo From"].toString());
    connect(SerialNoCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["SerialNo From"] = text;
        emit parametersChanged();
    });

    QGroupBox* SerialNoGroup = new QGroupBox("SerialNo", widget);
    SerialNoGroup->setStyleSheet(styleSheet);
    QFormLayout* SerialNoLayout = new QFormLayout(SerialNoGroup);
    SerialNoGroup->setLayout(SerialNoLayout);
    SerialNoLayout->addRow(tr("SerialNo: "), SerialNoEnable);
    SerialNoLayout->addRow(tr("SerialNo From:"), SerialNoCombo);
    layout->addRow(SerialNoGroup);


    //NetCode
    QCheckBox* NetCodeEnable = new QCheckBox(tr("Enable"), widget);
    NetCodeEnable->setChecked(m_params["NetCode"].toBool());
    connect(NetCodeEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["NetCode"] = checked;
        emit parametersChanged();
    });

    QComboBox* NetCodeCombo = new QComboBox(widget);
    NetCodeCombo->addItems({tr("Input"), tr("Mes"), tr("XlsxFile")});
    NetCodeCombo->setCurrentText(m_params["NetCode From"].toString());
    connect(NetCodeCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["NetCode From"] = text;
        emit parametersChanged();
    });

    QGroupBox* NetCodeGroup = new QGroupBox("NetCode", widget);
    NetCodeGroup->setStyleSheet(styleSheet);
    QFormLayout* NetCodeLayout = new QFormLayout(NetCodeGroup);
    NetCodeGroup->setLayout(NetCodeLayout);
    NetCodeLayout->addRow(tr("NetCode: "), NetCodeEnable);
    NetCodeLayout->addRow(tr("NetCode From:"), NetCodeCombo);
    layout->addRow(NetCodeGroup);

    return widget;
}

void InputCodeItem::selectXlsxFile()
{
    QString filePath = QFileDialog::getOpenFileName(nullptr,
                                                   tr("Select Xlsx File"),
                                                   "",
                                                   tr("Xlsx File (*.xlsx)"));
    if (!filePath.isEmpty()) {
        m_xlsxPathLineEdit->setText(filePath);
        m_params["XlsxFilePath"] = filePath;
        emit parametersChanged();
        emit xlsxFileSelected(filePath);
    }
}
