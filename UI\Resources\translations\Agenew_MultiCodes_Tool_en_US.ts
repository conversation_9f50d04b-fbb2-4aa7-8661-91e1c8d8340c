<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>AddItemDialog</name>
    <message>
        <source>No available test items to add.</source>
        <translation>No available test items to add.</translation>
    </message>
    <message>
        <source>Warning</source>
        <translation>Warning</translation>
    </message>
    <message>
        <source>Add Item</source>
        <translation type="unfinished">Add Item</translation>
    </message>
    <message>
        <source>Item Select: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>BottomCurrentItem</name>
    <message>
        <source>Max Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Min Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Time(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Timeout(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sleep Before Test(ms):</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CAboutDialog</name>
    <message>
        <source>(C) Copyright 2024 - %1 Agenew Inc. All rights reserved.</source>
        <translation>(C) Copyright 2024 - %1 Agenew Inc. All rights reserved.</translation>
    </message>
    <message>
        <source>About</source>
        <translation type="unfinished">About</translation>
    </message>
    <message>
        <source>(C) Copyright 2025 - 2035 Agenew Inc. All rights reserved.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Agenew MultiCodes Tool</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:11pt; font-weight:600; text-decoration: underline;&quot;&gt;Support List:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;MTK&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChargeCurrentItem</name>
    <message>
        <source>Max Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Min Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Time(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Timeout(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sleep Before Test(ms):</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CheckBarcodeFlagItem</name>
    <message>
        <source>Flag: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CheckCardItem</name>
    <message>
        <source>Enable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Check SIM1: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Check SIM2: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Check TCard: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Check NoCard: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeepSleepCurrentItem</name>
    <message>
        <source>Max Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Min Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Time(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Timeout(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sleep Before Test(ms):</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GetCsrFromDut</name>
    <message>
        <source>Select save path...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Browse...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Select CSR File Save Path</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Save Path:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Upload to YN System</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Upload Option:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputCodeItem</name>
    <message>
        <source>View...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>XlsxFile</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Section</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>YNSystem</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>YnSystem</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Select Xlsx File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Xlsx File (*.xlsx)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Common</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Xlsx File: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>YN Code: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SN: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SN From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IMEI1: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IMEI1 From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IMEI2: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IMEI2 From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>MEID: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>MEID From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac Prefix: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac Section Start: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac Section End: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac Section Next: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac Section Step: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac Prefix: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac Section Start: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac Section End: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac Section Next: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WifiMac Section Step: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>EthernetMac: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>EthernetMac From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SerialNo: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SerialNo From:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>NetCode: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>NetCode From:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <source>About</source>
        <translation>About</translation>
    </message>
    <message>
        <source>Clear Record</source>
        <translation>Clear Record</translation>
    </message>
    <message>
        <source>Language</source>
        <translation>Language</translation>
    </message>
    <message>
        <source>English</source>
        <translation>English</translation>
    </message>
    <message>
        <source>简体中文</source>
        <translation>Simplified Chinese</translation>
    </message>
    <message>
        <source>Add Item</source>
        <translation>Add Item</translation>
    </message>
    <message>
        <source>Delete Item</source>
        <translation>Delete Item</translation>
    </message>
    <message>
        <source>UnSaved Changes</source>
        <translation>UnSaved Changes</translation>
    </message>
    <message>
        <source>The parameters have been changed but have not been saved yet. Do you want to save them now ?</source>
        <translation>The parameters have been changed but have not been saved yet. Do you want to save them now?</translation>
    </message>
    <message>
        <source>Save Success!</source>
        <translation>Save Success!</translation>
    </message>
    <message>
        <source>Open Cfg File...</source>
        <translation>Open Cfg File...</translation>
    </message>
    <message>
        <source>Open Json File...</source>
        <translation>Open Json File...</translation>
    </message>
    <message>
        <source>Open APDB File...</source>
        <translation>Open APDB File...</translation>
    </message>
    <message>
        <source>Open MDDB File...</source>
        <translation>Open MDDB File...</translation>
    </message>
    <message>
        <source>Init Codes Xlsx</source>
        <translation>Init Codes Xlsx</translation>
    </message>
    <message>
        <source>The parameters have been changed and have not been saved yet</source>
        <translation>The parameters have been changed and have not been saved yet</translation>
    </message>
    <message>
        <source>TEE Version Changed</source>
        <translation>TEE Version Changed</translation>
    </message>
    <message>
        <source>TEE Version has been changed from %1 to %2.
The application needs to restart to apply the changes.
Do you want to restart now?</source>
        <translation>TEE Version has been changed from %1 to %2.
The application needs to restart to apply the changes.
Do you want to restart now?</translation>
    </message>
    <message>
        <source>Success</source>
        <translation>Success</translation>
    </message>
    <message>
        <source>Warning</source>
        <translation>Warning</translation>
    </message>
    <message>
        <source>TEE Version Mismatch</source>
        <translation>TEE Version Mismatch</translation>
    </message>
    <message>
        <source>TEE Version mismatch detected:
Configuration: %1
Current DLL: %2

Do you want to:
• Yes - Update configuration to match DLL version (%2)
• No - Update DLL files to match configuration (%1)</source>
        <translation>TEE Version mismatch detected:
Configuration: %1
Current DLL: %2

Do you want to:
• Yes - Update configuration to match DLL version (%2)
• No - Update DLL files to match configuration (%1)</translation>
    </message>
    <message>
        <source>TEE Version Update</source>
        <translation>TEE Version Update</translation>
    </message>
    <message>
        <source>DLL files prepared for update to version %1.
The application needs to restart to apply the changes.

Do you want to restart now?</source>
        <translation>DLL files prepared for update to version %1.
The application needs to restart to apply the changes.

Do you want to restart now?</translation>
    </message>
    <message>
        <source>DLL update script has been prepared.
Please restart the application manually to apply the changes to version %1.</source>
        <translation>DLL update script has been prepared.
Please restart the application manually to apply the changes to version %1.</translation>
    </message>
    <message>
        <source>TEE Version Update Failed</source>
        <translation>TEE Version Update Failed</translation>
    </message>
    <message>
        <source>Failed to update DLL files to version %1.
Please check if the version directory exists.</source>
        <translation>Failed to update DLL files to version %1.
Please check if the version directory exists.</translation>
    </message>
    <message>
        <source>TEE Version mismatch detected after loading configuration:
Configuration: %1
Current DLL: %2

Do you want to:
• Yes - Update configuration to match DLL version (%2)
• No - Update DLL files to match configuration (%1)</source>
        <translation>TEE Version mismatch detected after loading configuration:
Configuration: %1
Current DLL: %2

Do you want to:
• Yes - Update configuration to match DLL version (%2)
• No - Update DLL files to match configuration (%1)</translation>
    </message>
    <message>
        <source>Agenew_MultiCodes_Tool</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Home</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Setting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>More</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut1:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>P[...]</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>K[...]</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IWSN：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SerialNo：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SN:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>OBSN：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BatLabel：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WiFiMac: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>MEID: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IMEI1: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IMEI2: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>EtherMac：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>NetCode：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BtMac: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>ScrilSf：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Start</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>0:00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Count:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>0 %</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut2:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut3:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut4:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Common</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cfg File：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Log Path：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TestItem：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dut4</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use Relay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use ScanGun</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Meta Common</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wifi Only Project</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enable ADB Service</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Load AP DB From Dut</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Load MD DB From Dut</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Browse MD DB...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Browse AP DB...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Item Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Instrument</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Power Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Power Type：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Usb Control：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Battery</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Dual Channel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Single Channel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BlueBird</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>PowerSupply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Relay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Manual</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>GPIB ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Output1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Voltage：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>V</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Current：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>A</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Output2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>BLU Port：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cfg files(*.cfg)|*.cfg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Open json File...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>json files(*.json)|*.json</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Application will close and update DLL files, then restart automatically.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Failed to prepare DLL update. Please restart manually.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Database files(*.*)|*.*</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>No test case types registered.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MesSettingDialog</name>
    <message>
        <source>Mes Setting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mes Select</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>    Mes Type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>AGN_MES</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SD_MES</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>YD_MES</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mes Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Line: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IP Address :</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Station:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Order:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Operator:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>ResName:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mes Offline Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Not Check Station</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Not Update Station</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PowerOffCurrentItem</name>
    <message>
        <source>Max Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Min Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Time(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Timeout(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sleep Before Test(ms):</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PowerOnCurrentItem</name>
    <message>
        <source>Max Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Min Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Time(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Current(mA):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Trigger Timeout(s):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sleep Before Test(ms):</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <source>filename is empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>%1 is not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>%1 is not a regular file.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>%1 is not a readable file.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Open Directory</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ReadSwVerItem</name>
    <message>
        <source>System Property:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>verify:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SettingClass</name>
    <message>
        <source>  Dut1  </source>
        <translation>  Dut1  </translation>
    </message>
    <message>
        <source>  Dut2  </source>
        <translation>  Dut2  </translation>
    </message>
    <message>
        <source>  Dut3  </source>
        <translation>  Dut3  </translation>
    </message>
    <message>
        <source>  Dut4  </source>
        <translation>  Dut4  </translation>
    </message>
    <message>
        <source>Preloader</source>
        <translation>Preloader</translation>
    </message>
    <message>
        <source>Kernel</source>
        <translation>Kernel</translation>
    </message>
    <message>
        <source>ControlBox</source>
        <translation>ControlBox</translation>
    </message>
    <message>
        <source>ScanGun</source>
        <translation>ScanGun</translation>
    </message>
</context>
<context>
    <name>ShowLogDialog</name>
    <message>
        <source>ShowMainLog</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TeeSupportItem</name>
    <message>
        <source>Please select an Excel file containing Model and UID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Excel requirements for selecting Model and UID from table:
• Contains header row
• Model column + UID column
• Supports .xlsx/.xls
• Click ? button for details</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Select File...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Click to view detailed Excel format instructions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Remaining: Checking...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Ver:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Config File:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Select Model:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Status:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Select Model-UID Config File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Excel Files (*.xlsx *.xls);;All Files (*.*)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Success</source>
        <translation type="unfinished">Success</translation>
    </message>
    <message>
        <source>Excel file loaded successfully, found %1 models</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Failed to load Excel file:
%1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>No model data found</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Configuration Reminder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Model &apos;%1&apos; exists in the table, but the corresponding UID has changed.

Please reselect the model and apply the configuration.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The previously selected model &apos;%1&apos; does not exist in the current table.

Please reselect the appropriate model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Excel File Format Instructions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&lt;h3 style=&apos;color: #2E86AB;&apos;&gt;Excel file format requirements for selecting Model UID&lt;/h3&gt;&lt;p&gt;&lt;b&gt;File formats:&lt;/b&gt; Supports .xlsx and .xls&lt;/p&gt;&lt;p&gt;&lt;b&gt;Table Structure:&lt;/b&gt;&lt;/p&gt;&lt;ul style=&apos;margin-left: 20px;&apos;&gt;&lt;li&gt;&lt;b&gt;Must include a header row&lt;/b&gt; (first row is the header)&lt;/li&gt;&lt;li&gt;Model column titles: &lt;code&gt;Model&lt;/code&gt; / &lt;code&gt;型号&lt;/code&gt; / &lt;code&gt;model&lt;/code&gt; / &lt;code&gt;MODEL&lt;/code&gt;&lt;/li&gt;&lt;li&gt;UID column titles: &lt;code&gt;UID&lt;/code&gt; / &lt;code&gt;uid&lt;/code&gt; / &lt;code&gt;UUID&lt;/code&gt; / &lt;code&gt;uuid&lt;/code&gt;&lt;/li&gt;&lt;/ul&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Excel file example:

Row 1 (Header):
Column A     Column B
Model        UID

Row 2 and later (Data rows):
K3-P         8960_5cfbc611f8c3fe18f325a1f74a...
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Remaining: %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Remaining: 0 (Exhausted)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Remaining: No model selected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>TEE Remaining: Check failed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
