#ifndef WORKERTHREAD_H
#define WORKERTHREAD_H

#include <QObject>
#include <QThread>
#include "Common/Common.h"
#include "Instance/Meta/SmartPhoneSN.h"
#include "Instance/Visa/ConnectAgilent.h"
#include "Instance/MES/MesProxyBase.h"
#include "Instance/MES/MesFactory.h"

class WorkerThread : public QThread
{
    Q_OBJECT
public:
    WorkerThread(QObject *parent = nullptr,
                 SmartPhoneSN *MetaInstance = nullptr,
                 ConnectAgilent *VisaInstance = nullptr,
                 MesProxyBase *MesProxy = nullptr,
                 UINT nTreadID = 0);
    ~WorkerThread();

protected:
    void run() override;

signals:
    void signal_ClearUIlogs(UINT nTreadID);
    void signal_UpdateResult(UINT nTreadID, TestResult_Status Status);
    void signal_UpdateResultToMes(UINT nTreadID, TestResult_Status Status, QString barcode, UINT TestTime);
    void signal_UpdateUILog(UINT nTreadID, QString log, LOGCOLOR_TYPE textcolor = BLACK);
    void signal_EnableUIItem(UINT nTreadID);
    void signal_UpdateMainUIItem(UINT nTreadID, QString m_ItemName, TestResult_Status m_Status, QString value="");
    void signal_UpdateBarcodeToUI(UINT nTreadID, QString m_Barcode);
    void signal_UpdateUIPortInfo(UINT nTreadID);
    void signal_InitItemListView(UINT nTreadID);
    void signal_SetLogPath(UINT nTreadID);
    void signal_RenameLogFile(UINT nTreadID, QString newName);
    void signal_checkstatus(UINT nTreadID);
    void signal_StartTime(UINT nTreadID);
    void signal_send_data(UINT nTreadID, QString data);
    void signal_UploadMacToYnSystem(UINT nTreadID, QString Order, QString SN, QString BtMac, QString WifiMac);

private:
    void ClearUIlogs();
    void UpdateResult(TestResult_Status Status);
    void UpdateResultToMes(TestResult_Status Status, QString barcode, UINT TestTime);
    void UpdateUILog(QString log, LOGCOLOR_TYPE textcolor = BLACK);
    void EnableUIItem();
    void UpdateTaskListItem(QString m_ItemName, TestResult_Status m_Status, QString value="");
    void UpdateBarcodeToUI(QString m_Barcode);
    void UpdateUIPortInfo();
    void InitItemListView();
    void InitPowerStatus();
    bool NeedPowerSupply();
    bool PowerOffCurrentTest(float* value);
    bool PowerOnCurrentTest(float* value, float* maxValue);
    bool DeepSleepCurrentTest(float* value);
    bool ChargeCurrentTest(float* value);
    bool BottomCurrentTest(float* value);
    void SetLogPath();
    void getTestItemList();
    bool CheckFixtureStatus();
    QString stringOfBits(const QString &decimalString);
    void ControlUsb(bool enable);
    void OpenPowerSupply();
    void ClosePowerSupply();
    double GetCurrentFromPowerSupply();
    bool UploadMacToYnSystem(QString Order, QString SN, QString BtMac, QString WifiMac);

private:
    UINT m_nTreadID;
    QStringList TestItemList;
    ScanData_struct m_sScanData;
	bool BoxStatus, BoxLastStatus;
	UsbControlType UsbControl;
    bool m_bPowerSupplyConnect;

private:
    SmartPhoneSN    *m_pMetaInstance;
    ConnectAgilent  *m_pVisaInstance;
    MesProxyBase    *m_pMesProxy;
};

#endif // WORKERTHREAD_H
