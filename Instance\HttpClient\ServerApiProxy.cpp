#include "ServerApiProxy.h"

ServerApiProxy::ServerApiProxy(QObject *parent,
                               HttpClient *httpclient,
                               UINT nTreadID)
    : QThread(parent)
    , m_httpclient(httpclient)
    , m_nTreadID(nTreadID)
{

}

ServerApiProxy::~ServerApiProxy(void)
{
}

QString ServerApiProxy::GetMacFromZentao(QString &Order, QString &SN)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(GET_MAC_ADDRESS) + "-" + Order + "-" + SN;

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "GetMacFromZentao address: " << address;
    return m_httpclient->HttpGet(address);
}

QString ServerApiProxy::UploadMacToZentao(QString &Order, QString &SN, QString &BtMac, QString &WifiMac)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(UPLOAD_MAC_ADDRESS) + "-" + Order + "-" + SN + "-" + BtMac + "-" + WifiMac;

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "UploadMacToZentao address: " << address;
    return m_httpclient->HttpGet(address);
}

QString ServerApiProxy::CheckMacFromZentao(QString &Order, QString &BtMac, QString &WifiMac)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(CHECK_MAC_ADDRESS) + "-" + Order + "-" + BtMac + "-" + WifiMac;

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "CheckMacFromZentao address: " << address;
    return m_httpclient->HttpGet(address);
}

QString ServerApiProxy::UploadCsrToZentao(QString &SN, QString &model, QString &fingerprint, QString &chipid, QString &content)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(UPLOAD_CSR_ADDRESS);
    QString data = QString("sn=%1&model=%2&fingerprint=%3&chipid=%4&content=%5").arg(SN).arg(model).arg(fingerprint).arg(chipid).arg(content);

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "UploadMacToZentao address: " << address;
    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "UploadMacToZentao Data: " << data;
    return m_httpclient->HttpPost(address, data, "application/x-www-form-urlencoded");
}
