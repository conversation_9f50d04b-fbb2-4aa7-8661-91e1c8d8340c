#include "CheckBarcodeFlagItem.h"

CheckBarcodeFlagItem::CheckBarcodeFlagItem()
{
    m_params = {
        {"Flag", "60:1;61:0;62:P"},
    };
}

QWidget* CheckBarcodeFlagItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    QLineEdit *FlagEdit = new QLineEdit(widget);
    FlagEdit->setText(m_params["Flag"].toString());
    connect(FlagEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["Flag"] = text;
        emit parametersChanged();
    });
    layout->addRow(tr("Flag: "), FlagEdit);

    return widget;
}
