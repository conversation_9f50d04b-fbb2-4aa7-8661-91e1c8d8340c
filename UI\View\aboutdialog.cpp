#include "aboutdialog.h"
#include "ui_aboutdialog.h"
#include <QDate>
#include <QEvent>

CAboutDialog::CAboutDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::CAboutDialog)
{
    // 去掉标题栏上的问号图标
    Qt::WindowFlags flags = this->windowFlags();
    flags &= ~Qt::WindowContextHelpButtonHint;
    this->setWindowFlags(flags);

    ui->setupUi(this);
    QDate date = QDate::currentDate();
    QString copyRight = tr("(C) Copyright 2024 - %1 Agenew Inc. All rights reserved.").arg(date.year());
    ui->m_lbCopyRight->setText(copyRight);
}

CAboutDialog::~CAboutDialog()
{
    delete ui;
}

void CAboutDialog::changeEvent(QEvent* event)
{
    if (event && event->type() == QEvent::LanguageChange) {
        ui->retranslateUi(this);
        QDate date = QDate::currentDate();
        QString copyRight = tr("(C) Copyright 2024 - %1 Agenew Inc. All rights reserved.").arg(date.year());
        ui->m_lbCopyRight->setText(copyRight);
    }
    QDialog::changeEvent(event);
}
