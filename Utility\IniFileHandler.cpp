#include "IniFileHandler.h"
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QRegularExpression>
#include <QDateTime>
#include <QDebug>
#include <QStandardPaths>

IniFileHandler::IniFileHandler()
{
}

IniFileHandler::~IniFileHandler()
{
}

QString IniFileHandler::getExeDirectory()
{
    return QApplication::applicationDirPath();
}

QString IniFileHandler::getKphEnvIniPath()
{
    QString exeDir = getExeDirectory();
    QString relativePath = "tee_stuff/kph_in/kph_env.ini";
    
    // 规范化路径，防止路径遍历攻击
    QDir baseDir(exeDir);
    QString canonicalPath = baseDir.absoluteFilePath(relativePath);
    QFileInfo pathInfo(canonicalPath);
    
    // 确保路径在exe目录范围内
    if (!pathInfo.absoluteFilePath().startsWith(exeDir)) {
        qWarning() << "检测到潜在的路径遍历攻击";
        return QString();
    }
    
    return canonicalPath;
}

bool IniFileHandler::checkKphEnvIniExists()
{
    QString iniPath = getKphEnvIniPath();
    return QFile::exists(iniPath);
}

bool IniFileHandler::validateUidFormat(const QString& uid)
{
    if (uid.isEmpty() || uid.length() < 8 || uid.length() > 128) {
        return false;
    }
    
    // 更严格的UID格式验证
    // 支持常见的UID格式: 纯十六进制或带下划线分隔的十六进制
    QRegularExpression uidPattern("^[0-9A-Fa-f]+(_[0-9A-Fa-f]+)*$");
    QRegularExpressionMatch match = uidPattern.match(uid);
    
    return match.hasMatch() && match.captured() == uid;
}

bool IniFileHandler::ensureDirectoryExists(const QString& dirPath)
{
    QDir dir(dirPath);
    if (!dir.exists()) {
        return dir.mkpath(dirPath);
    }
    return true;
}

QString IniFileHandler::getCurrentKeyboxUuid()
{
    QString iniPath = getKphEnvIniPath();
    if (!QFile::exists(iniPath)) {
        return QString();
    }
    
    QSettings settings(iniPath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");
    
    return settings.value("vturkey/keybox_uuid", "").toString();
}

bool IniFileHandler::writeUidToKphEnvIni(const QString& uid, QString& errorInfo)
{
    if (!validateUidFormat(uid)) {
        errorInfo = QString("UID格式不正确: %1").arg(uid);
        return false;
    }
    
    QString iniPath = getKphEnvIniPath();
    QFileInfo fileInfo(iniPath);
    
    // 确保目录存在
    if (!ensureDirectoryExists(fileInfo.absolutePath())) {
        errorInfo = QString("无法创建目录: %1").arg(fileInfo.absolutePath());
        return false;
    }
    
    // 如果文件不存在，创建默认的kph_env.ini文件
    if (!QFile::exists(iniPath)) {
        if (!createDefaultKphEnvIni(iniPath, errorInfo)) {
            return false;
        }
    }
    
    // 使用QSettings更新keybox_uuid值
    QSettings settings(iniPath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");
    
    // 读取当前值
    QString currentUid = settings.value("vturkey/keybox_uuid", "").toString();
    
    // 设置新的UID值
    settings.setValue("vturkey/keybox_uuid", uid);
    settings.sync();
    
    // 验证是否写入成功
    if (settings.status() != QSettings::NoError) {
        errorInfo = QString("写入kph_env.ini文件失败，QSettings错误");
        return false;
    }
    
    // 再次读取验证
    QString verifyUid = getCurrentKeyboxUuid();
    if (verifyUid != uid) {
        errorInfo = QString("写入验证失败，期望: %1, 实际: %2").arg(uid).arg(verifyUid);
        return false;
    }
    
    qDebug() << QString("成功写入UID到kph_env.ini: %1 -> %2").arg(currentUid).arg(uid);
    return true;
}

bool IniFileHandler::createDefaultKphEnvIni(const QString& filePath, QString& errorInfo)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        errorInfo = QString("无法创建kph_env.ini文件: %1").arg(filePath);
        return false;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    // 写入默认的kph_env.ini内容
    out << "; Configuration for KPHA" << Qt::endl;
    out << Qt::endl;
    out << "[core]" << Qt::endl;
    out << "forceinit = true        ; Whether a device is allowed to be exported multiple times" << Qt::endl;
    out << "do_upload = false" << Qt::endl;
    out << "ifaa = false" << Qt::endl;
    out << "soter = false" << Qt::endl;
    out << "secondary_cert = true" << Qt::endl;
    out << "vturkey = true" << Qt::endl;
    out << "rkp = false" << Qt::endl;
    out << "rkpforce = false" << Qt::endl;
    out << Qt::endl;
    out << "[log]" << Qt::endl;
    out << "notice_silent = false   ; If `false`, popup a message box for notice happens (e.g. kpha_init completes)" << Qt::endl;
    out << "err_silent = false      ; If `false`, popup a message box if error happens" << Qt::endl;
    out << Qt::endl;
    out << "[errata]" << Qt::endl;
    out << "errata_1000001 = false  ; for service meta_tst not in late_start mode" << Qt::endl;
    out << "errata_2000001 = false  ; for kph failing to work with non-rpmb device" << Qt::endl;
    out << "errata_2000002 = false  ; for kph writing finish bit for export_dev_inf" << Qt::endl;
    out << Qt::endl;
    out << "[vturkey]" << Qt::endl;
    out << "url = http://116.62.230.138/vturkey" << Qt::endl;
    out << "keybox_uuid =   ; keybox-uuid for vturkey request" << Qt::endl;
    out << "install_keybox = true ; RKP and keybox must be enabled at the same time" << Qt::endl;
    out << Qt::endl;
    out << "[rkp]" << Qt::endl;
    out << "test_mode   = false       ; whether to export CSR in test mode" << Qt::endl;
    out << "readsn      = false        ; if value is true,read serialno;if value is false read barcode.default value is true" << Qt::endl;
    out << "readimei    = true        ;" << Qt::endl;
    out << "barcode_index = 0" << Qt::endl;
    out << "saveAsFile  = false" << Qt::endl;
    out << "savePath    = " << Qt::endl;
    out << Qt::endl;
    out << "[kus]" << Qt::endl;
    out << "server = https://pl.trustkernel.com/kus/   ; URL to key-upload-server" << Qt::endl;
    
    file.close();
    
    qDebug() << QString("成功创建默认kph_env.ini文件: %1").arg(filePath);
    return true;
}