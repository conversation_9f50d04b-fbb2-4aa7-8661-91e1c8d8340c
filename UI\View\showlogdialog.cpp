#include "showlogdialog.h"
#include "ui_showlogdialog.h"

ShowLogDialog::ShowLogDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::ShowLogDialog)
{
    ui->setupUi(this);
}

ShowLogDialog::~ShowLogDialog()
{
    delete ui;
}

void ShowLogDialog::InitLogText(QString Text)
{
    ui->MainLogText->setHtml(Text);
}

void ShowLogDialog::changeEvent(QEvent* event)
{
    if (event && event->type() == QEvent::LanguageChange) {
        ui->retranslateUi(this);
    }
    QDialog::changeEvent(event);
}
