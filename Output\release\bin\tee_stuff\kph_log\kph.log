 [09 12 12:49:57][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 12:49:57][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 12:49:57][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 12:49:57][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 12:49:57][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 12:49:57][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 12:49:57][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 12:49:57][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 12:58:20][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 12:58:20][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 12:58:20][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 12:58:20][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 12:58:20][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 12:58:20][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 12:58:20][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 12:58:20][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 12:58:24][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 12:58:24][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 12:58:24][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 12:58:24][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 12:58:24][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 12:58:24][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 12:58:24][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 12:58:24][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 12:58:26][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 12:58:26][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 12:58:26][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 12:58:26][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 12:58:26][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 12:58:26][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 12:58:26][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 12:58:26][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 12:58:30][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 12:58:30][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 12:58:30][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 12:58:30][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 12:58:30][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 12:58:30][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 12:58:30][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 12:58:30][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:18:47][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:18:47][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:18:47][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:18:47][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:18:47][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:18:47][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 13:18:47][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:18:47][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:18:54][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:18:54][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:18:54][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:18:54][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:18:54][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:18:54][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:18:55][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:18:55][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:19: 1][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:19: 1][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:19: 1][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:19: 1][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:19: 1][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:19: 1][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:19: 1][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:19: 1][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:26:33][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:26:33][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:26:33][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:26:33][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:26:33][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:26:33][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:26:34][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:26:34][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:26:39][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:26:39][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:26:39][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:26:39][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:26:39][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:26:39][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:26:39][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:26:39][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:27:14][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:27:14][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:27:14][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:27:14][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:27:14][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:27:14][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:27:14][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:27:14][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:27:36][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:27:36][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:27:36][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:27:36][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:27:36][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:27:36][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:27:36][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:27:43][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:27:43][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:27:43][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:27:43][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:27:43][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:27:43][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:27:43][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:27:43][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:28: 3][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:28: 3][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:28: 3][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:28: 3][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:28: 3][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:28: 3][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:28: 4][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:28: 4][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:28:19][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:28:19][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:28:19][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:28:19][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:28:19][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:28:19][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:28:19][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:28:19][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:31:36][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:31:36][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:31:36][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:31:36][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:31:36][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:31:36][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:31:36][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:31:43][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:31:43][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:31:43][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:31:43][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:31:43][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:31:43][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 13:31:43][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:31:43][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32: 0][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32: 0][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:32: 0][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32: 0][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32: 0][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:32: 0][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:32: 0][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32: 6][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32: 6][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32: 6][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:32: 6][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:32: 6][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:32: 6][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:32: 6][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:32: 6][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32:26][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32:26][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32:26][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32:26][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32:26][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32:26][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32:42][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32:42][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32:42][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32:42][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32:42][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32:42][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32:54][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32:54][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32:54][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:32:54][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:32:54][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:32:54][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:33: 1][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:33: 1][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:33: 1][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:33:28][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:33:28][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:33:28][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:33:28][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:33:28][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:33:28][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:33:28][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34: 4][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34: 4][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34: 4][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34: 4][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34: 4][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34: 4][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34:17][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34:17][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34:17][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34:45][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34:45][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:34:45][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34:45][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34:45][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 13:34:45][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:34:45][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34:58][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34:58][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:34:58][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:34:58][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:34:58][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:34:58][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:34:58][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:35: 9][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:35: 9][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:35: 9][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:35: 9][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:35: 9][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:35: 9][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:35: 9][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:35: 9][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:37: 0][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:37: 0][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:37: 0][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:37: 0][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:37: 0][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:37: 0][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:37:12][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:37:12][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:37:12][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:37:12][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:37:12][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:37:12][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:39:41][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:39:41][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:39:41][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:39:41][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:39:41][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 13:39:41][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:39:41][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 13:39:48][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 13:39:48][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 13:39:48][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 13:39:48][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 13:39:48][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 13:39:48][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 13:39:48][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 13:39:48][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:27:42][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:27:42][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:27:42][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:27:42][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:27:42][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 14:27:42][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:27:42][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:27:45][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:27:45][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:27:45][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:27:45][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:27:45][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:27:45][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 14:27:46][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:27:46][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:27:50][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:27:50][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:27:50][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:27:50][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:27:50][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 14:27:50][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:27:50][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:28: 4][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:28: 4][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:28: 4][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 14:28: 4][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 14:28: 4][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 14:28: 4][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 14:28: 4][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:22:40][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:22:40][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:22:40][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:22:40][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:22:40][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:22:40][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:22:40][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:22:44][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:22:44][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:22:44][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:22:44][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:22:44][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:22:44][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:22:44][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:22:51][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:22:51][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:22:51][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:22:51][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:22:51][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:22:51][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:22:51][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:24:28][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:24:28][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:24:28][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:24:28][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:24:28][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:24:28][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:24:28][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:14][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:14][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:14][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:14][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:14][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:14][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:29:15][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:15][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:23][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:23][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:23][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:23][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:23][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:29:23][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:23][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:29][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:29][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:29][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:29][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:29][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:29:29][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:29][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:43][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:43][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:43][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:43][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:43][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:29:43][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:43][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:52][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:52][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:52][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:29:52][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:29:52][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:29:52][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:29:52][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:32: 7][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:32: 7][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:32: 7][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:32: 7][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:32: 7][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 87 out of 87 bytes
>
 [09 12 15:32: 7][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:32: 7][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:32:11][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:32:11][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:32:11][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 15:32:11][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 15:32:11][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 15:32:11][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 15:32:11][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:19][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:19][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:19][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:19][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:19][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:19][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:19][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:24][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:24][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:24][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:24][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:24][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:24][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:24][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:39][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:39][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:39][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:39][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:39][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:39][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:39][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:47][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:47][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:47][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:47][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:47][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:47][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:47][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:54][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:54][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:54][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:54][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:54][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:54][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:54][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:59][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:59][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:59][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 16:54:59][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 16:54:59][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 16:54:59][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 16:54:59][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 19:26:30][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 19:26:30][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 19:26:30][INFO]<kpha_reinit_env>: kpha_env_inited = 0
 [09 12 19:26:30][INFO]<kpha_init_env>: Load Config from "E:\tool_develop\mydevelop\Agenew_MultiCodes_Tool\Output\release\bin\tee_stuff\kph_in\kph_env.ini" OK.
 [09 12 19:26:30][INFO]<getLicenseCount>: use VTurkeyLicenser
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <  Trying **************...
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <TCP_NODELAY set
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <Connected to ************** (**************) port 80 (#0)
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <upload completely sent off: 88 out of 88 bytes
>
 [09 12 19:26:30][ERR]<trustkernel::curl_debug>: connect info: <Connection #0 to host ************** left intact
>
 [09 12 19:26:30][INFO]<kpha_reinit_env>: kpha_env_inited = 0
