## 项目概览（Agenew_MultiCodes_Tool / MTK 多串口测试工具）

- 目标：基于 Qt5.14.2 + MSVC2017 的 Windows 桌面应用，用于 MTK 设备在 Meta 模式下的多工位/多串口产测，覆盖写码、核码、版本校验、电流测试、MES 对接等关键流程。
- 入口：main.cpp -> MainWindow（Qt UI）
- 平台/依赖：Qt、Windows API、串口(QSerialPort)、网络(QNetworkAccessManager)、第三方 SDK（BLUAPI、KPHA、Meta 等）
- 生成时间：2025-09-12

## 架构总览

应用按“UI 驱动 + 业务实例层 + 通用能力层 + 第三方”分层：
- UI 层（UI/、mainwindow.*、UI/View/…）：界面和交互，承载测试项编排、日志与状态展示
- 指令与线程（Cmd/…）：界面回调与工作线程封装
- 实例层（Instance/…）：面向领域的功能模块
  - HttpClient：后端 API 访问、JSON 处理
  - LoadXlsx：测试项/码表 EXCEL 加载
  - MES：Agenew/BlueBird/YD 多种 MES 代理工厂
  - Meta：序列号/设备信息抽象
  - SerialPort：串口通信封装
  - TestItem：测试项框架与具体用例（写码/核码/电流等）
  - Visa：台体/仪器对接（如安捷伦电源）
- 通用与工具（Common/、Utility/…）：
  - Common：全局配置、数据模型、测试流程常量、全局状态（含 TEE 版本管理）
  - Utility：文件/INI/版本/通用工具
- 第三方（3rdParty/…）：SDK 和外部库

### Mermaid 结构图（高层）
```mermaid
flowchart TD
    A[main.cpp] --> B[MainWindow(UI)]
    B --> C[Cmd]
    B --> D[Common]
    B --> E[TestItem Framework]
    E -->|uses| D
    D --> F[Utility]
    E --> G[Instance: HttpClient]
    E --> H[Instance: LoadXlsx]
    E --> I[Instance: MES]
    E --> J[Instance: Meta]
    E --> K[Instance: SerialPort]
    E --> L[Instance: Visa]
    subgraph Instance
      G
      H
      I
      J
      K
      L
    end
    subgraph ThirdParty
      T1[BLUAPI]
      T2[KPHA SDK]
      T3[Meta/Flash Tools]
    end
    L --> T1
    J --> T2
    D --> G
```

## 模块索引（点击进入模块说明）
- [Cmd/CLAUDE.md](Cmd/CLAUDE.md)
- [Common/CLAUDE.md](Common/CLAUDE.md)
- [Utility/CLAUDE.md](Utility/CLAUDE.md)
- [UI/CLAUDE.md](UI/CLAUDE.md)
- [Instance/HttpClient/CLAUDE.md](Instance/HttpClient/CLAUDE.md)
- [Instance/LoadXlsx/CLAUDE.md](Instance/LoadXlsx/CLAUDE.md)
- [Instance/MES/CLAUDE.md](Instance/MES/CLAUDE.md)
- [Instance/Meta/CLAUDE.md](Instance/Meta/CLAUDE.md)
- [Instance/SerialPort/CLAUDE.md](Instance/SerialPort/CLAUDE.md)
- [Instance/TestItem/CLAUDE.md](Instance/TestItem/CLAUDE.md)
- [Instance/Visa/CLAUDE.md](Instance/Visa/CLAUDE.md)

## 全局规范与约定
- 编译：Qt 5.14.2 + MSVC2017；build.bat 提供构建脚本（见 CLAUDE.local.md）
- 平台：Windows；涉及 Windows.h 与串口/共享内存等能力
- 命名/语言：C++/Qt 风格；中文注释与界面文案混用
- 线程：界面与工作线程（Cmd/WorkerThread）分离；注意跨线程 UI 更新使用信号/槽
- 日志：UI/View/showlogdialog 等组件承载；Common 中提供计数/状态
- 配置：INI/JSON/Excel 三源；Common 统一对外 get/set 接口
- TEE/SDK：Common 维护 TEE 版本与 DLL 同步校验；3rdParty 下放置厂商 SDK

## 关键入口/运行路径
- 程序入口：main.cpp -> QApplication -> MainWindow::show -> app.exec()
- 单实例：QSharedMemory 以 appName 作为 key 防重复启动
- UI 主窗：mainwindow.h/.cpp 提供大量槽函数驱动测试流程
- 测试项装配：Common::defaultOrderList 与 TestItemFactory/TestCase 决定

## 后续建议
- 为 TestItem/TestCase 下各用例补充独立说明与时序图
- 增加自动化单元测试（如关键工具类 Utility/* 与 Common 逻辑）
- 对 3rdParty SDK 的版本与接口变更建立追踪表

