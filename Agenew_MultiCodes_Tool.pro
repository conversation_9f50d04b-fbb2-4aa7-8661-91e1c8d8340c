QT       += core gui xml serialport network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

QMAKE_CXXFLAGS += /utf-8
DEFINES -= UNICODE
DEFINES += UMBCS
QMAKE_CXXFLAGS -= -Zc:strictStrings

LIBS += -lshlwapi

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# 程序版本
VERSION = 1.0
# 公司名称
QMAKE_TARGET_COMPANY = "Agenew"
# 程序说明
QMAKE_TARGET_DESCRIPTION = "Agenew MultiCodes Tool"
# 版权信息
QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2025 Agenew"
# 程序名称
QMAKE_TARGET_PRODUCT = "Agenew_MultiCodes_Tool"

SOURCES += \
    3rdParty/DebugTrace/src/xboot_debug.cpp \
    3rdParty/KPHA-SDK-v5731/KPHAProxy.cpp \
    Cmd/MainWindowCallback.cpp \
    Cmd/WorkerThread.cpp \
    Common/Common.cpp \
    Instance/HttpClient/HttpClient.cpp \
    Instance/HttpClient/ServerApiProxy.cpp \
    Instance/LoadXlsx/QxlsxInstance.cpp \
    Instance/MES/AgenewMesProxy.cpp \
    Instance/MES/BirdMesProxy.cpp \
    Instance/MES/MesFactory.cpp \
    Instance/MES/MesProxyBase.cpp \
    Instance/MES/YDMesProxy.cpp \
    Instance/Meta/SNBase.cpp \
    Instance/Meta/SmartPhoneSN.cpp \
    Instance/SerialPort/serialport.cpp \
    Instance/TestItem/TestCase/BottomCurrentItem.cpp \
    Instance/TestItem/TestCase/ChargeCurrentItem.cpp \
    Instance/TestItem/TestCase/CheckBarcodeFlagItem.cpp \
    Instance/TestItem/TestCase/CheckBatLevelItem.cpp \
    Instance/TestItem/TestCase/CheckCardItem.cpp \
    Instance/TestItem/TestCase/CheckCodes.cpp \
    Instance/TestItem/TestCase/DeepSleepCurrentItem.cpp \
    Instance/TestItem/TestCase/EnterModeItem.cpp \
    Instance/TestItem/TestCase/FactoryResetItem.cpp \
    Instance/TestItem/TestCase/GetCsrFromDut.cpp \
    Instance/TestItem/TestCase/InputCodeeItem.cpp \
    Instance/TestItem/TestCase/InputYDCodesItem.cpp \
    Instance/TestItem/TestCase/LoadSnItem.cpp \
    Instance/TestItem/TestCase/PowerOffCurrentItem.cpp \
    Instance/TestItem/TestCase/PowerOffItem.cpp \
    Instance/TestItem/TestCase/PowerOnCurrentItem.cpp \
    Instance/TestItem/TestCase/ReadSwVerItem.cpp \
    Instance/TestItem/TestCase/RebootItem.cpp \
    Instance/TestItem/TestCase/TeeCheckItem.cpp \
    Instance/TestItem/TestCase/TeeSupportItem.cpp \
    Instance/TestItem/TestCase/WriteCodesItem.cpp \
    Instance/TestItem/TestCaseBase.cpp \
    Instance/TestItem/TestCaseFactory.cpp \
    Instance/Visa/ConnectAgilent.cpp \
    UI/View/aboutdialog.cpp \
    UI/View/additemdialog.cpp \
    UI/View/messettingdialog.cpp \
    UI/View/settingclass.cpp \
    UI/View/showlogdialog.cpp \
    Utility/FileUtils.cpp \
    Utility/IniFileHandler.cpp \
    Utility/IniItem.cpp \
    Utility/ModelUidReader.cpp \
    Utility/Utils.cpp \
    Utility/version.cpp \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    3rdParty/BLUAPI/inc/BLUAPI.h \
    3rdParty/DebugTrace/inc/Mdebug.h \
    3rdParty/DebugTrace/inc/xboot_debug.h \
    3rdParty/KPHA-SDK-v5731/KPHAProxy.h \
    3rdParty/KPHA-SDK-v5731/inc/LibKPHA.h \
    3rdParty/KPHA-SDK-v5731/inc/LibKpaUtil.h \
    3rdParty/KPHA-SDK-v5731/inc/LibTurkey.h \
    3rdParty/KPHA-SDK-v5731/inc/libuploader.h \
    3rdParty/Meta/inc/C2kAgent_api.h \
    3rdParty/Meta/inc/C2kAgent_api_datatype.h \
    3rdParty/Meta/inc/FtaAgent_api.h \
    3rdParty/Meta/inc/HdcpEncryption.h \
    3rdParty/Meta/inc/SLA_Challenge.h \
    3rdParty/Meta/inc/meta.h \
    3rdParty/Meta/inc/meta_boot_def.h \
    3rdParty/Meta/inc/meta_core.h \
    3rdParty/Meta/inc/meta_dll_audio.h \
    3rdParty/Meta/inc/meta_dll_connectivity.h \
    3rdParty/Meta/inc/meta_dll_gps.h \
    3rdParty/Meta/inc/meta_dll_mm.h \
    3rdParty/Meta/inc/meta_dll_nfc.h \
    3rdParty/Meta/inc/mtk_mcu.h \
    3rdParty/Meta/inc/sp_brom.h \
    3rdParty/Meta/inc/sp_conn_para.h \
    3rdParty/Meta/inc/sp_md_info.h \
    3rdParty/Meta/inc/xboot.h \
    3rdParty/Visa/inc/visa.h \
    3rdParty/Visa/inc/visatype.h \
    Cmd/MainWindowCallback.h \
    Cmd/WorkerThread.h \
    Common/3rdPartyDeviceCmd.h \
    Common/Common.h \
    Common/TestItemInfo.h \
    Instance/HttpClient/HttpClient.h \
    Instance/HttpClient/JsonHandler.h \
    Instance/HttpClient/ServerApiProxy.h \
    Instance/LoadXlsx/QxlsxInstance.h \
    Instance/MES/AgenewMesProxy.h \
    Instance/MES/BirdMesProxy.h \
    Instance/MES/MesDataUnit.h \
    Instance/MES/MesFactory.h \
    Instance/MES/MesProxyBase.h \
    Instance/MES/YDMesProxy.h \
    Instance/Meta/SNBase.h \
    Instance/Meta/SmartPhoneSN.h \
    Instance/SerialPort/serialport.h \
    Instance/TestItem/DraggableTreeWidget.h \
    Instance/TestItem/TestCase/BottomCurrentItem.h \
    Instance/TestItem/TestCase/ChargeCurrentItem.h \
    Instance/TestItem/TestCase/CheckBarcodeFlagItem.h \
    Instance/TestItem/TestCase/CheckBatLevelItem.h \
    Instance/TestItem/TestCase/CheckCardItem.h \
    Instance/TestItem/TestCase/CheckCodes.h \
    Instance/TestItem/TestCase/DeepSleepCurrentItem.h \
    Instance/TestItem/TestCase/EnterModeItem.h \
    Instance/TestItem/TestCase/FactoryResetItem.h \
    Instance/TestItem/TestCase/GetCsrFromDut.h \
    Instance/TestItem/TestCase/InputCodeItem.h \
    Instance/TestItem/TestCase/InputYDCodesItem.h \
    Instance/TestItem/TestCase/LoadSnItem.h \
    Instance/TestItem/TestCase/PowerOffCurrentItem.h \
    Instance/TestItem/TestCase/PowerOffItem.h \
    Instance/TestItem/TestCase/PowerOnCurrentItem.h \
    Instance/TestItem/TestCase/ReadSwVerItem.h \
    Instance/TestItem/TestCase/RebootItem.h \
    Instance/TestItem/TestCase/TeeCheckItem.h \
    Instance/TestItem/TestCase/TeeSupportItem.h \
    Instance/TestItem/TestCase/WriteCodesItem.h \
    Instance/TestItem/TestCaseBase.h \
    Instance/TestItem/TestCaseFactory.h \
    Instance/TestItem/TestItem_HeaderFile.h \
    Instance/Visa/ConnectAgilent.h \
    UI/View/aboutdialog.h \
    UI/View/additemdialog.h \
    UI/View/messettingdialog.h \
    UI/View/settingclass.h \
    UI/View/showlogdialog.h \
    Utility/FileUtils.h \
    Utility/IniFileHandler.h \
    Utility/IniItem.h \
    Utility/ModelUidReader.h \
    Utility/Utils.h \
    Utility/version.h \
    Utility/version_def.h \
    mainwindow.h

FORMS += \
    UI/View/aboutdialog.ui \
    UI/View/additemdialog.ui \
    UI/View/messettingdialog.ui \
    UI/View/showlogdialog.ui \
    mainwindow.ui

TRANSLATIONS += \
    $$PWD/UI/Resources/translations/Agenew_MultiCodes_Tool_en_US.ts \
    $$PWD/UI/Resources/translations/Agenew_MultiCodes_Tool_zh_CN.ts

CONFIG += lrelease
CONFIG += embed_translations

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

build_type =
CONFIG(debug, debug|release) {
    build_type = debug
} else {
    build_type = release
}

OUT_PWD = $$PWD/Output
DESTDIR = $$PWD/Output/$$build_type/bin
MOC_DIR = $$PWD/Output/$$build_type/moc
OBJECTS_DIR = $$PWD/Output/$$build_type/obj
UI_DIR = $$PWD/Output/$$build_type/ui
RCC_DIR = $$PWD/Output/$$build_type/qrc

RESOURCES += \
    UI/Resources/icon.qrc \
    UI/Resources/translations.qrc

RC_ICONS = UI/Resources/images/Agenew_MultiCodes_Tool.ico


# Copy qm files to Output Path.
copy_files.files += \
    $$PWD/UI/Resources/translations/Agenew_MultiCodes_Tool_en_US.qm \
    $$PWD/UI/Resources/translations/Agenew_MultiCodes_Tool_zh_CN.qm
copy_files.path = $$PWD/Output/$$build_type
COPIES += copy_files

LIBS += -L$$PWD/3rdParty/Meta/lib/ -lMetaCore
LIBS += -L$$PWD/3rdParty/DebugTrace/lib/ -lMTRACE
LIBS += -L$$PWD/3rdParty/KPHA-SDK-v5731/lib/ -lLibKpaUtil -lLibTurkey -lLibUploader #-lLibKPHA
LIBS += -L$$PWD/3rdParty/Visa/lib/ -lvisa32
LIBS += -L$$PWD/3rdParty/BLUAPI/lib/ -lBLUAPI

INCLUDEPATH += $$PWD/3rdParty/Meta/inc \
                $$PWD/3rdParty/DebugTrace/inc \
                $$PWD/3rdParty/KPHA-SDK-v5731/inc \
                $$PWD/3rdParty/Visa/inc \
                $$PWD/3rdParty/BLUAPI/inc

DEPENDPATH += $$PWD/3rdParty/Meta/lib \
                $$PWD/3rdParty/DebugTrace/lib \
                $$PWD/3rdParty/KPHA-SDK-v5731/lib \
                $$PWD/3rdParty/Visa/lib \
                $$PWD/3rdParty/BLUAPI/lib
		
QXLSX_PARENTPATH=$$PWD/3rdParty/QXlsx/
QXLSX_HEADERPATH=$$PWD/3rdParty/QXlsx/header/
QXLSX_SOURCEPATH=$$PWD/3rdParty/QXlsx/source/
include($$PWD/3rdParty/QXlsx/QXlsx.pri)

