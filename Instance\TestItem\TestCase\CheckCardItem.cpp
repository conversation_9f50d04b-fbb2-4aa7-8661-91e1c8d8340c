#include "CheckCardItem.h"

CheckCardItem::CheckCardItem()
{
    m_params = {
        {"Check SIM1", true},
        {"Check SIM2", true},
        {"Check TCard", true},
        {"Check NoCard", false},
    };
}

QWidget* CheckCardItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    //Check SIM1
    QCheckBox* CheckSim1Enable = new QCheckBox(tr("Enable"), widget);
    CheckSim1Enable->setChecked(m_params["Check SIM1"].toBool());
    connect(CheckSim1Enable, &QCheckBox::toggled, [this](bool checked) {
        m_params["Check SIM1"] = checked;
        emit parametersChanged();
    });
    layout->addRow(tr("Check SIM1: "), CheckSim1Enable);

    //Check SIM2
    QCheckBox* CheckSim2Enable = new QCheckBox(tr("Enable"), widget);
    CheckSim2Enable->setChecked(m_params["Check SIM2"].toBool());
    connect(CheckSim2Enable, &QCheckBox::toggled, [this](bool checked) {
        m_params["Check SIM2"] = checked;
        emit parametersChanged();
    });
    layout->addRow(tr("Check SIM2: "), CheckSim2Enable);

    //Check TCard
    QCheckBox* CheckTCardEnable = new QCheckBox(tr("Enable"), widget);
    CheckTCardEnable->setChecked(m_params["Check TCard"].toBool());
    connect(CheckTCardEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["Check TCard"] = checked;
        emit parametersChanged();
    });
    layout->addRow(tr("Check TCard: "), CheckTCardEnable);

    //Check No Card
    QCheckBox* CheckNoCardEnable = new QCheckBox(tr("Enable"), widget);
    CheckNoCardEnable->setChecked(m_params["Check NoCard"].toBool());
    connect(CheckNoCardEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["Check NoCard"] = checked;
        emit parametersChanged();
    });
    layout->addRow(tr("Check NoCard: "), CheckNoCardEnable);

    return widget;
}
