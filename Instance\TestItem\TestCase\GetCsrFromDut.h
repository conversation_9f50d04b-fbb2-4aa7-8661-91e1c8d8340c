#ifndef GETCSRFROMDUT_H
#define GETCSRFROMDUT_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QLineEdit>
#include <QFormLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QFileDialog>
#include <QStandardPaths>

class GetCsrFromDut : public TestCaseBase
{
    Q_OBJECT
public:
    GetCsrFromDut();

    QString type() const override { return "GetCsrFromDut"; }
    static QString staticType() { return "GetCsrFromDut"; }
    QString displayName() const override { return "Get Csr From Dut"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // GETCSRFROMDUT_H
