#ifndef MESSETTINGDIALOG_H
#define MESSETTINGDIALOG_H

#include <QDialog>

namespace Ui {
class MesSettingDialog;
}

class MainWindow;

class MesSettingDialog : public QDialog
{
    Q_OBJECT

public:
    explicit MesSettingDialog(MainWindow *mainwindow, QWidget *parent = nullptr);
    ~MesSettingDialog();

    void InitSettings();
    void SaveSettings();

protected:
    void changeEvent(QEvent* event) override;

private slots:
    void on_MesSelect_comboBox_currentTextChanged(const QString &arg1);

    void on_buttonBox_accepted();

private:
    Ui::MesSettingDialog *ui;
    MainWindow *m_mainwindow;
};

#endif // MESSETTINGDIALOG_H
