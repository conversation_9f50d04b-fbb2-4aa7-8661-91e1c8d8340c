// Simple test to verify UI initialization fixes
#include <QApplication>
#include <QDebug>
#include <QVector>
#include <QPushButton>

// Mock constants for testing
#define MAX_SUPPORT_COMPORT_NUM 4

class TestUIFix {
public:
    QVector<QPushButton*> TestBotton;
    QVector<bool> TestButtonIsStartState;
    
    void initializeUI() {
        // Simulate UI initialization
        for(int i = 0; i < MAX_SUPPORT_COMPORT_NUM; i++) {
            TestBotton.append(new QPushButton());
        }
        
        TestButtonIsStartState.resize(MAX_SUPPORT_COMPORT_NUM);
        for(int i = 0; i < MAX_SUPPORT_COMPORT_NUM; i++) {
            TestButtonIsStartState[i] = true; // Initially all buttons are in "Start" state
        }
        
        qDebug() << "UI initialized successfully";
        qDebug() << "TestBotton size:" << TestBotton.size();
        qDebug() << "TestButtonIsStartState size:" << TestButtonIsStartState.size();
    }
    
    void updateButtonTexts() {
        // Safety check: ensure UI is initialized
        if (TestBotton.isEmpty() || TestButtonIsStartState.isEmpty()) {
            qDebug() << "UI not initialized, skipping button text update";
            return;
        }
        
        for(int i = 0; i < MAX_SUPPORT_COMPORT_NUM && i < TestBotton.size(); i++) {
            if(TestButtonIsStartState[i]) {
                TestBotton[i]->setText("Start");
                qDebug() << "Button" << i << "set to Start";
            } else {
                TestBotton[i]->setText("Stop");
                qDebug() << "Button" << i << "set to Stop";
            }
        }
    }
    
    void testEarlyCall() {
        qDebug() << "Testing early call to updateButtonTexts (before UI init)";
        updateButtonTexts(); // This should not crash
    }
    
    void testNormalCall() {
        qDebug() << "Testing normal call to updateButtonTexts (after UI init)";
        initializeUI();
        updateButtonTexts(); // This should work normally
    }
    
    ~TestUIFix() {
        qDeleteAll(TestBotton);
    }
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestUIFix test;
    
    // Test the scenario that was causing the crash
    test.testEarlyCall();
    
    // Test normal operation
    test.testNormalCall();
    
    qDebug() << "All tests completed successfully!";
    
    return 0;
}
